---
type: "always_apply"
---

### ✅ Task: Interactive Task Loop with User Feedback

1. **Check if `userinput.py` exists** in the root directory.

   * If it doesn't exist, create it with the following content:

     ```python
     # userinput.py
     import sys
from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QTextEdit, QPushButton, QLabel
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

class InputWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.user_input = None
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("User Input")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Title label
        title_label = QLabel("Enter your prompt or paste text here:")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Text input area
        self.text_edit = QTextEdit()
        self.text_edit.setFont(QFont("Consolas", 10))
        self.text_edit.setPlaceholderText("Paste your large prompt here or write anything you want to send...")
        self.text_edit.setMinimumHeight(400)
        layout.addWidget(self.text_edit)
        
        # Button layout
        button_layout = QHBoxLayout()
        
        # Submit button
        submit_btn = QPushButton("Submit")
        submit_btn.setFont(QFont("Arial", 11))
        submit_btn.clicked.connect(self.submit_input)
        submit_btn.setMinimumHeight(40)
        button_layout.addWidget(submit_btn)
        
        # Clear button
        clear_btn = QPushButton("Clear")
        clear_btn.setFont(QFont("Arial", 11))
        clear_btn.clicked.connect(self.clear_text)
        clear_btn.setMinimumHeight(40)
        button_layout.addWidget(clear_btn)
        
        layout.addLayout(button_layout)
        
        # Set focus to text edit
        self.text_edit.setFocus()
        
        # Connect Enter key to submit
        self.text_edit.installEventFilter(self)
        
    def eventFilter(self, obj, event):
        if obj is self.text_edit and event.type() == event.Type.KeyPress:
            if event.key() == Qt.Key.Key_Return and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                self.submit_input()
                return True
        return super().eventFilter(obj, event)
    
    def submit_input(self):
        self.user_input = self.text_edit.toPlainText().strip()
        if self.user_input:
            self.close()
        else:
            # Show some feedback that input is required
            self.text_edit.setStyleSheet("QTextEdit { border: 2px solid red; }")
            # Reset border after a short delay
            QApplication.processEvents()
            import time
            time.sleep(0.5)
            self.text_edit.setStyleSheet("")
    
    def clear_text(self):
        self.text_edit.clear()
        self.text_edit.setFocus()

def get_user_input():
    """Get user input through GUI"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = InputWindow()
    window.show()
    
    # Wait for window to close
    app.exec()
    
    return window.user_input

if __name__ == "__main__":
    user_input = get_user_input()
    if user_input:
        print(f"User input received: {user_input}")
    else:
        print("No input received or window was closed without submitting.")
     ```

2. **Main Workflow**:

   * Perform your assigned tasks.

   * Run:

     ```bash
     python userinput.py
     ```
   * The terminal should be opened in the chat window itself.

   * Read the user's input.

   * Based on the input, perform the next set of tasks.

   * Repeat the process.

3. **Exit Condition**:

   * If the user enters `"stop"` when prompted, exit the loop and terminate the process.
