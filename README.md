# 🤖 ZARA Voice Assistant 2.0

**Advanced AI Voice Assistant with Real-time Multimodal Capabilities**

Created by: **<PERSON>nam Sanjay**

---

## 🌟 Overview

ZARA 2.0 is a sophisticated voice assistant built with the LiveKit framework, featuring advanced AI capabilities, comprehensive system control, and extensive automation tools. It combines real-time voice processing, computer vision, and intelligent task execution in a seamless bilingual (Hindi/English) interface.

## ✨ Key Features

### 🎤 **Voice & Audio**
- Real-time voice processing with Google's "Charon" voice
- Noise cancellation and audio enhancement
- Bilingual support (Hindi/English)
- Natural conversation flow

### 👁️ **Computer Vision & Screen Monitoring**
- Live camera feed analysis
- Intelligent frame sampling
- Scene understanding and description
- Visual context integration
- **Real-time screen capture and analysis**
- **Live screen monitoring with change detection**
- **Precise mouse cursor control and positioning**
- **Activity hotspot detection and tracking**

### 🛠️ **System Control**
- Power management (shutdown/restart/lock)
- Window management and automation
- Application launching and control
- Keyboard and mouse simulation

### 📊 **Data Analysis**
- Excel/CSV data processing
- Advanced visualization generation
- Business intelligence insights
- Automated report generation

### 🌐 **Web & Communication**
- Web search (Wikipedia + DuckDuckGo)
- Email automation via Gmail
- WhatsApp message automation
- Weather information retrieval

### 🔒 **Security & Monitoring**
- System virus scanning
- Network security auditing
- Performance monitoring
- Resource usage tracking

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Windows/Linux/macOS
- LiveKit account and credentials
- Microphone and camera (optional)

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd ZARA
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Configure environment:**
   - Copy `.env.example` to `.env`
   - Add your LiveKit credentials:
```env
LIVEKIT_URL=wss://your-livekit-url
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret
```

4. **Run ZARA:**
```bash
python run_zara.py
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `LIVEKIT_URL` | LiveKit server URL | ✅ |
| `LIVEKIT_API_KEY` | LiveKit API key | ✅ |
| `LIVEKIT_API_SECRET` | LiveKit API secret | ✅ |
| `USER_NAME` | Preferred name for greetings | ❌ |
| `GMAIL_USER` | Gmail for email automation | ❌ |
| `GMAIL_PASSWORD` | Gmail app password | ❌ |

### LiveKit Setup

ZARA is pre-configured with your LiveKit credentials:
- **URL:** `wss://nova-izk011h8.livekit.cloud`
- **API Key:** `APIv6QoStypqbd3`
- **API Secret:** `VMpCabBbOHMKUavQVFW05ONhjb4wOnuUFGApvezaXVA`

## 🎯 Available Commands

### 🌤️ **Weather & Information**
- "Delhi का मौसम बताओ" - Get weather information
- "आज का समय क्या है?" - Current time and date
- "सिस्टम की जानकारी दो" - System diagnostics

### 💻 **System Control**
- "कंप्यूटर बंद करो" - Shutdown system
- "विंडो बंद करो" - Close active window
- "डेस्कटॉप दिखाओ" - Show desktop

### 📱 **Communication**
- "John को WhatsApp भेजो" - Send WhatsApp message
- "Email भेजो" - Send email
- "नोटपैड में लिखो" - Create document

### 📊 **Data Analysis**
- "Excel analyze करो" - Analyze spreadsheet data
- "ग्राफ बनाओ" - Create visualizations
- "रिपोर्ट जेनरेट करो" - Generate reports

### 🔒 **Security**
- "वायरस स्कैन करो" - Run virus scan
- "नेटवर्क चेक करो" - Network security audit

### 🖥️ **Screen Monitoring & Control**
- "स्क्रीन कैप्चर करो" - Capture and analyze current screen
- "माउस को (x,y) पर ले जाओ" - Move mouse to specific coordinates
- "माउस की स्थिति बताओ" - Get current mouse position
- "स्क्रीन एक्टिविटी मॉनिटर करो" - Monitor screen changes over time
- "स्क्रीन में बदलाव देखो" - Detect changes in screen regions

### 🔄 **Continuous Screen Monitoring (ADVANCED)**
- "स्क्रीन मॉनिटरिंग शुरू करो" - Start real-time screen monitoring
- "स्क्रीन कंटेक्स्ट बताओ" - Get current screen analysis
- "फॉर्म भरने में मदद करो" - Task-specific screen assistance
- "स्क्रीन मॉनिटरिंग बंद करो" - Stop continuous monitoring

## 🏗️ Architecture

### Core Components

```
ZARA/
├── Zara_Voice_Assistant.py    # Main agent class
├── tools.py                   # Tool implementations
├── prompts.py                 # AI instructions
├── run_zara.py               # Launcher script
├── requirements.txt          # Dependencies
├── .env                      # Configuration
└── assets/                   # Media files
```

### Technology Stack

- **Framework:** LiveKit Agents
- **AI Model:** Google Realtime API
- **Voice:** Google "Charon" voice
- **Language:** Python 3.11+
- **GUI:** PyQt5
- **Data:** Pandas, NumPy, Matplotlib
- **Automation:** PyAutoGUI

## 🎨 Personality

ZARA embodies:
- **Respectful:** Always addresses users with respect
- **Intelligent:** Technical expertise with emotional understanding
- **Bilingual:** Seamless Hindi-English communication
- **Proactive:** Offers helpful suggestions and follow-ups
- **Reliable:** Consistent performance and error handling

## 🔧 Development

### Adding New Tools

1. Create function in `tools.py`:
```python
@function_tool()
async def my_new_tool(param: str) -> str:
    """Tool description"""
    # Implementation
    return "Result"
```

2. Add to tool list in `Zara_Voice_Assistant.py`

3. Update prompts in `prompts.py` if needed

### Debugging

Enable debug mode:
```env
TORCH_COMPILE_DEBUG=1
TORCHDYNAMO_VERBOSE=1
```

View logs:
```bash
tail -f zara.log
```

## 📝 License

This project is created by Ratnam Sanjay for educational and personal use.

## 🤝 Support

For issues and questions:
1. Check the logs in `zara.log`
2. Verify environment configuration
3. Ensure all dependencies are installed
4. Check LiveKit connection status

## 🎉 Acknowledgments

- **Creator:** Ratnam Sanjay
- **Framework:** LiveKit Team
- **AI Models:** Google AI
- **Inspiration:** Advanced voice assistant technology

---

**ZARA 2.0** - Your Intelligent Voice Companion 🚀
