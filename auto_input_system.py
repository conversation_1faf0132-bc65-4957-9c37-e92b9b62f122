#!/usr/bin/env python3
"""
ZARA Automatic Input Box Detection and Voice Text Entry System
Created by: <PERSON><PERSON> Sanjay

This module provides advanced input field detection, voice command parsing,
and automatic text entry based on user voice commands.
"""

import cv2
import numpy as np
import pyautogui
import re
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import asyncio
import logging

# Configure Tesseract if available
try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

@dataclass
class InputField:
    """Represents a detected input field"""
    field_type: str  # text, email, password, search, number, etc.
    position: Tuple[int, int, int, int]  # x, y, width, height
    label: str  # associated label text
    placeholder: str  # placeholder text if any
    confidence: float
    is_active: bool
    field_id: str  # unique identifier

@dataclass
class VoiceCommand:
    """Parsed voice command for text entry"""
    action: str  # fill, enter, type, clear, etc.
    target_field: str  # field identifier or description
    text_content: str  # text to enter
    field_type: str  # inferred field type
    confidence: float

class AutoInputDetector:
    """Advanced input field detection system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Input field detection patterns
        self.field_patterns = {
            'text_input': {
                'visual_cues': ['rectangular', 'border', 'cursor', 'text_area'],
                'size_range': (50, 20, 800, 60),  # min_width, min_height, max_width, max_height
                'aspect_ratio': (2.0, 20.0)  # min, max aspect ratio
            },
            'button': {
                'visual_cues': ['rectangular', 'border', 'clickable'],
                'size_range': (40, 20, 300, 80),
                'aspect_ratio': (1.0, 8.0)
            },
            'dropdown': {
                'visual_cues': ['rectangular', 'arrow', 'border'],
                'size_range': (80, 20, 400, 40),
                'aspect_ratio': (2.0, 15.0)
            }
        }
        
        # Field type keywords for classification
        self.field_keywords = {
            'email': ['email', 'e-mail', 'mail', 'ईमेल', '@'],
            'password': ['password', 'pass', 'pwd', 'पासवर्ड', 'गुप्त'],
            'name': ['name', 'नाम', 'first', 'last', 'full'],
            'phone': ['phone', 'mobile', 'number', 'फोन', 'मोबाइल', 'संख्या'],
            'address': ['address', 'पता', 'location', 'street'],
            'search': ['search', 'खोज', 'find', 'query'],
            'username': ['username', 'user', 'login', 'यूजर'],
            'age': ['age', 'उम्र', 'years', 'साल'],
            'date': ['date', 'तारीख', 'day', 'month', 'year'],
            'message': ['message', 'comment', 'text', 'संदेश', 'टिप्पणी']
        }
        
        # Voice command patterns
        self.command_patterns = {
            'fill': [
                r'fill (?:the )?(.+?) (?:field |box |input )?with (.+)',
                r'enter (.+) in (?:the )?(.+?) (?:field|box|input)',
                r'type (.+) in (?:the )?(.+)',
                r'(.+?) में (.+) भरो',
                r'(.+?) फील्ड में (.+) डालो'
            ],
            'clear': [
                r'clear (?:the )?(.+?) (?:field|box|input)',
                r'empty (?:the )?(.+)',
                r'(.+?) को साफ करो',
                r'(.+?) खाली करो'
            ],
            'focus': [
                r'click (?:on )?(?:the )?(.+?) (?:field|box|input)',
                r'select (?:the )?(.+)',
                r'(.+?) पर क्लिक करो',
                r'(.+?) को चुनो'
            ]
        }
    
    async def detect_input_fields(self, screenshot=None) -> List[InputField]:
        """Detect all input fields on the current screen"""
        try:
            # Take screenshot if not provided
            if screenshot is None:
                screenshot = await asyncio.to_thread(pyautogui.screenshot)
            
            screenshot_np = np.array(screenshot)
            
            # Convert to different color spaces for analysis
            gray = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2GRAY)
            hsv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2HSV)
            
            # Detect input fields using multiple methods
            fields = []
            
            # Method 1: Edge detection for rectangular shapes
            edge_fields = await self._detect_by_edges(gray)
            fields.extend(edge_fields)
            
            # Method 2: Color-based detection (white/light backgrounds)
            color_fields = await self._detect_by_color(hsv)
            fields.extend(color_fields)
            
            # Method 3: OCR-based detection (if available)
            if TESSERACT_AVAILABLE:
                ocr_fields = await self._detect_by_ocr(screenshot_np)
                fields.extend(ocr_fields)
            
            # Method 4: Template matching for common UI elements
            template_fields = await self._detect_by_templates(gray)
            fields.extend(template_fields)
            
            # Remove duplicates and merge overlapping fields
            unique_fields = self._merge_duplicate_fields(fields)
            
            # Classify field types based on context
            classified_fields = await self._classify_field_types(unique_fields, screenshot_np)
            
            return classified_fields
            
        except Exception as e:
            self.logger.error(f"Input field detection failed: {e}")
            return []
    
    async def _detect_by_edges(self, gray_image) -> List[InputField]:
        """Detect input fields using edge detection"""
        fields = []
        
        try:
            # Apply edge detection
            edges = cv2.Canny(gray_image, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                # Get bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)
                
                # Filter by size and aspect ratio
                if self._is_valid_input_field(w, h):
                    # Check if it looks like an input field
                    confidence = self._calculate_input_confidence(gray_image[y:y+h, x:x+w])
                    
                    if confidence > 0.3:
                        fields.append(InputField(
                            field_type='text',
                            position=(x, y, w, h),
                            label='',
                            placeholder='',
                            confidence=confidence,
                            is_active=False,
                            field_id=f"edge_{x}_{y}"
                        ))
            
        except Exception as e:
            self.logger.error(f"Edge detection failed: {e}")
        
        return fields
    
    async def _detect_by_color(self, hsv_image) -> List[InputField]:
        """Detect input fields by color characteristics"""
        fields = []
        
        try:
            # Define color ranges for typical input fields (white/light gray)
            lower_white = np.array([0, 0, 200])
            upper_white = np.array([180, 30, 255])
            
            # Create mask for white/light areas
            mask = cv2.inRange(hsv_image, lower_white, upper_white)
            
            # Find contours in the mask
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                
                if self._is_valid_input_field(w, h):
                    confidence = 0.4  # Base confidence for color detection
                    
                    fields.append(InputField(
                        field_type='text',
                        position=(x, y, w, h),
                        label='',
                        placeholder='',
                        confidence=confidence,
                        is_active=False,
                        field_id=f"color_{x}_{y}"
                    ))
            
        except Exception as e:
            self.logger.error(f"Color detection failed: {e}")
        
        return fields
    
    async def _detect_by_ocr(self, screenshot_np) -> List[InputField]:
        """Detect input fields using OCR to find labels and placeholders"""
        fields = []
        
        try:
            # Get text data from OCR
            text_data = await asyncio.to_thread(
                pytesseract.image_to_data,
                screenshot_np,
                output_type=pytesseract.Output.DICT
            )
            
            # Analyze text to find input field indicators
            for i in range(len(text_data['text'])):
                if int(text_data['conf'][i]) > 30:
                    text = text_data['text'][i].strip().lower()
                    
                    # Check if text indicates an input field
                    if self._is_field_indicator(text):
                        x, y, w, h = (text_data['left'][i], text_data['top'][i],
                                    text_data['width'][i], text_data['height'][i])
                        
                        # Look for nearby input field
                        field_position = self._find_nearby_input_field(screenshot_np, x, y, w, h)
                        
                        if field_position:
                            field_type = self._classify_field_by_text(text)
                            
                            fields.append(InputField(
                                field_type=field_type,
                                position=field_position,
                                label=text,
                                placeholder='',
                                confidence=0.7,
                                is_active=False,
                                field_id=f"ocr_{x}_{y}"
                            ))
            
        except Exception as e:
            self.logger.error(f"OCR detection failed: {e}")
        
        return fields
    
    async def _detect_by_templates(self, gray_image) -> List[InputField]:
        """Detect input fields using template matching"""
        fields = []
        
        try:
            # Create simple templates for common input field patterns
            templates = self._create_input_templates()
            
            for template_name, template in templates.items():
                # Perform template matching
                result = cv2.matchTemplate(gray_image, template, cv2.TM_CCOEFF_NORMED)
                locations = np.where(result >= 0.6)
                
                for pt in zip(*locations[::-1]):
                    x, y = pt
                    w, h = template.shape[1], template.shape[0]
                    
                    fields.append(InputField(
                        field_type='text',
                        position=(x, y, w, h),
                        label='',
                        placeholder='',
                        confidence=0.6,
                        is_active=False,
                        field_id=f"template_{template_name}_{x}_{y}"
                    ))
            
        except Exception as e:
            self.logger.error(f"Template detection failed: {e}")
        
        return fields
    
    def _is_valid_input_field(self, width: int, height: int) -> bool:
        """Check if dimensions are valid for an input field"""
        # Minimum size requirements
        if width < 50 or height < 15:
            return False
        
        # Maximum size requirements
        if width > 800 or height > 100:
            return False
        
        # Aspect ratio check
        aspect_ratio = width / height
        if aspect_ratio < 1.5 or aspect_ratio > 25:
            return False
        
        return True
    
    def _calculate_input_confidence(self, field_region) -> float:
        """Calculate confidence that a region is an input field"""
        try:
            # Check for typical input field characteristics
            confidence = 0.0
            
            # Check for rectangular shape
            if field_region.shape[0] > 10 and field_region.shape[1] > 30:
                confidence += 0.2
            
            # Check for border-like edges
            edges = cv2.Canny(field_region, 50, 150)
            edge_density = np.sum(edges > 0) / (field_region.shape[0] * field_region.shape[1])
            
            if 0.1 < edge_density < 0.4:  # Moderate edge density suggests borders
                confidence += 0.3
            
            # Check for uniform background
            std_dev = np.std(field_region)
            if std_dev < 30:  # Low variation suggests uniform background
                confidence += 0.2
            
            # Check brightness (input fields are often light)
            mean_brightness = np.mean(field_region)
            if mean_brightness > 200:
                confidence += 0.3
            
            return min(confidence, 1.0)
            
        except Exception:
            return 0.0
    
    def _is_field_indicator(self, text: str) -> bool:
        """Check if text indicates presence of an input field"""
        indicators = [
            'name', 'email', 'password', 'phone', 'address', 'search',
            'username', 'login', 'register', 'sign', 'enter', 'type',
            'नाम', 'ईमेल', 'पासवर्ड', 'फोन', 'पता', 'खोज'
        ]
        
        return any(indicator in text for indicator in indicators)
    
    def _classify_field_by_text(self, text: str) -> str:
        """Classify field type based on associated text"""
        text_lower = text.lower()
        
        for field_type, keywords in self.field_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return field_type
        
        return 'text'  # Default type
    
    def _find_nearby_input_field(self, image, text_x, text_y, text_w, text_h) -> Optional[Tuple[int, int, int, int]]:
        """Find input field near detected text label"""
        # Search area around the text
        search_radius = 100
        
        # Common positions relative to label
        search_areas = [
            (text_x + text_w + 10, text_y - 5, 200, text_h + 10),  # Right of label
            (text_x, text_y + text_h + 5, text_w + 100, 30),       # Below label
            (text_x - 210, text_y - 5, 200, text_h + 10),          # Left of label
        ]
        
        for search_x, search_y, search_w, search_h in search_areas:
            # Ensure search area is within image bounds
            search_x = max(0, search_x)
            search_y = max(0, search_y)
            search_w = min(search_w, image.shape[1] - search_x)
            search_h = min(search_h, image.shape[0] - search_y)
            
            if search_w > 0 and search_h > 0:
                # Extract search region
                search_region = image[search_y:search_y+search_h, search_x:search_x+search_w]
                
                # Look for input field characteristics in this region
                if self._has_input_field_characteristics(search_region):
                    return (search_x, search_y, search_w, search_h)
        
        return None
    
    def _has_input_field_characteristics(self, region) -> bool:
        """Check if a region has input field characteristics"""
        try:
            gray = cv2.cvtColor(region, cv2.COLOR_RGB2GRAY) if len(region.shape) == 3 else region
            
            # Check for rectangular borders
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                if self._is_valid_input_field(w, h):
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _create_input_templates(self) -> Dict[str, np.ndarray]:
        """Create templates for common input field patterns"""
        templates = {}
        
        # Simple rectangular input field template
        rect_template = np.zeros((30, 150), dtype=np.uint8)
        cv2.rectangle(rect_template, (2, 2), (147, 27), 255, 2)
        templates['rectangle'] = rect_template
        
        # Rounded input field template
        rounded_template = np.zeros((35, 180), dtype=np.uint8)
        cv2.rectangle(rounded_template, (5, 5), (175, 30), 255, 2)
        templates['rounded'] = rounded_template
        
        return templates
    
    def _merge_duplicate_fields(self, fields: List[InputField]) -> List[InputField]:
        """Remove duplicate and overlapping input fields"""
        if not fields:
            return []
        
        # Sort by confidence (highest first)
        fields.sort(key=lambda f: f.confidence, reverse=True)
        
        unique_fields = []
        
        for field in fields:
            is_duplicate = False
            
            for existing_field in unique_fields:
                if self._fields_overlap(field, existing_field):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_fields.append(field)
        
        return unique_fields
    
    def _fields_overlap(self, field1: InputField, field2: InputField) -> bool:
        """Check if two fields overlap significantly"""
        x1, y1, w1, h1 = field1.position
        x2, y2, w2, h2 = field2.position
        
        # Calculate overlap area
        overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
        overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
        overlap_area = overlap_x * overlap_y
        
        # Calculate areas
        area1 = w1 * h1
        area2 = w2 * h2
        
        # Check if overlap is significant (>50% of smaller field)
        min_area = min(area1, area2)
        return overlap_area > (min_area * 0.5)
    
    async def _classify_field_types(self, fields: List[InputField], screenshot_np) -> List[InputField]:
        """Classify the types of detected input fields"""
        classified_fields = []
        
        for field in fields:
            # Extract field region for analysis
            x, y, w, h = field.position
            field_region = screenshot_np[y:y+h, x:x+w]
            
            # Analyze surrounding text for context
            context_text = await self._get_field_context(screenshot_np, field.position)
            
            # Classify based on context
            field_type = self._classify_field_by_text(context_text)
            
            # Update field with classification
            classified_field = InputField(
                field_type=field_type,
                position=field.position,
                label=context_text,
                placeholder=field.placeholder,
                confidence=field.confidence,
                is_active=field.is_active,
                field_id=field.field_id
            )
            
            classified_fields.append(classified_field)
        
        return classified_fields
    
    async def _get_field_context(self, screenshot_np, position: Tuple[int, int, int, int]) -> str:
        """Get contextual text around an input field"""
        if not TESSERACT_AVAILABLE:
            return ""
        
        try:
            x, y, w, h = position
            
            # Expand search area around the field
            context_margin = 50
            context_x = max(0, x - context_margin)
            context_y = max(0, y - context_margin)
            context_w = min(screenshot_np.shape[1] - context_x, w + 2 * context_margin)
            context_h = min(screenshot_np.shape[0] - context_y, h + 2 * context_margin)
            
            # Extract context region
            context_region = screenshot_np[context_y:context_y+context_h, context_x:context_x+context_w]
            
            # Get text from context region
            text = await asyncio.to_thread(pytesseract.image_to_string, context_region)
            
            return text.strip().lower()
            
        except Exception:
            return ""

# Global instance
auto_input_detector = AutoInputDetector()
