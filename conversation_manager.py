#!/usr/bin/env python3
"""
ZARA Natural Conversation Manager
Created by: <PERSON><PERSON>jay

This module handles natural conversation flow, context retention,
proactive assistance, and human-like interaction patterns.
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import random
from personality_engine import zara_personality, analyze_user_emotion, get_personality_response
from relationship_memory import advanced_memory

@dataclass
class ConversationContext:
    """Current conversation context"""
    topic: str
    subtopics: List[str]
    user_intent: str
    emotional_state: str
    conversation_stage: str  # greeting, main, followup, closing
    last_user_message: str
    last_zara_response: str
    context_history: List[Dict]
    started_at: datetime
    last_activity: datetime

@dataclass
class ProactiveAssistance:
    """Proactive assistance suggestion"""
    suggestion_type: str
    content: str
    confidence: float
    context: str
    timing: str  # immediate, delayed, scheduled

class NaturalConversationManager:
    """Manages natural conversation flow and context"""
    
    def __init__(self):
        self.current_context: Optional[ConversationContext] = None
        self.conversation_history: List[ConversationContext] = []
        
        # Conversation patterns
        self.greeting_patterns = {
            'morning': [
                "Good morning! I hope you're having a great start to your day! 🌅",
                "सुप्रभात! आज का दिन कैसा जा रहा है?",
                "Morning! Ready to tackle some interesting tasks together? ☀️"
            ],
            'afternoon': [
                "Good afternoon! How's your day going so far? 😊",
                "नमस्ते! दोपहर कैसी जा रही है?",
                "Afternoon! Hope you're having a productive day! 🌞"
            ],
            'evening': [
                "Good evening! How was your day? 🌆",
                "शुभ संध्या! दिन कैसा रहा?",
                "Evening! Ready to wind down or still working on something? 🌙"
            ]
        }
        
        self.conversation_starters = {
            'first_time': [
                "Hi there! I'm ZARA, your AI assistant. I'm excited to get to know you and help with whatever you need! 😊",
                "नमस्ते! मैं ZARA हूं, आपकी AI सहायक। मैं आपकी मदद करने के लिए यहां हूं!",
                "Welcome! I'm ZARA, and I'm here to make your digital life easier and more enjoyable! ✨"
            ],
            'returning_user': [
                "Welcome back! Great to see you again! 😊",
                "वापस आपका स्वागत है! आपको फिर से देखकर खुशी हुई!",
                "Hey there! Nice to have you back! What can we work on today? 🎯"
            ],
            'regular_user': [
                "Hi! Ready for another productive session together? 💪",
                "हैलो! आज क्या नया करने का मन है?",
                "Hey! What interesting challenge shall we tackle today? 🚀"
            ]
        }
        
        self.follow_up_patterns = {
            'success': [
                "Great! That worked out well. Anything else I can help with? 🎉",
                "Perfect! Is there something else you'd like to explore? ✨",
                "Wonderful! What's next on your agenda? 🎯"
            ],
            'partial_success': [
                "That's a good start! Should we refine this further? 🔧",
                "We're getting there! Want to try a different approach? 💡",
                "Not bad! Let's see if we can make it even better! 📈"
            ],
            'need_clarification': [
                "I want to make sure I understand correctly. Could you tell me more about...? 🤔",
                "Let me clarify this so I can help you better... 💭",
                "I'm not quite sure I got that right. Can you help me understand...? 🧐"
            ]
        }
        
        self.proactive_suggestions = {
            'productivity': [
                "I noticed you often work on documents. Would you like me to help organize your files?",
                "Since you use spreadsheets frequently, shall I show you some advanced Excel tricks?",
                "I see you're multitasking a lot. Want me to help you set up a better workflow?"
            ],
            'learning': [
                "You seem curious about technology! Would you like me to explain something interesting?",
                "I noticed you ask great questions. Want to explore a new topic together?",
                "You're always learning! Shall I suggest some resources for your interests?"
            ],
            'wellness': [
                "You've been working hard! Maybe it's time for a short break? 🌱",
                "I notice you work late sometimes. Want me to remind you about healthy work habits?",
                "How about we set up some reminders for staying hydrated and taking breaks? 💧"
            ]
        }
    
    def start_conversation(self, user_message: str) -> Dict[str, Any]:
        """Start a new conversation"""
        # Analyze user emotion and intent
        emotion_analysis = analyze_user_emotion(user_message)
        
        # Determine user type (first time, returning, regular)
        user_patterns = advanced_memory.analyze_user_patterns()
        relationship_strength = user_patterns.get('relationship_strength', 0.0)
        
        if relationship_strength < 0.2:
            user_type = 'first_time'
        elif relationship_strength < 0.6:
            user_type = 'returning_user'
        else:
            user_type = 'regular_user'
        
        # Get time-appropriate greeting
        current_hour = datetime.now().hour
        if 5 <= current_hour < 12:
            time_period = 'morning'
        elif 12 <= current_hour < 17:
            time_period = 'afternoon'
        else:
            time_period = 'evening'
        
        # Create conversation context
        self.current_context = ConversationContext(
            topic='greeting',
            subtopics=[],
            user_intent='initiate_conversation',
            emotional_state=emotion_analysis['detected_emotion'],
            conversation_stage='greeting',
            last_user_message=user_message,
            last_zara_response='',
            context_history=[],
            started_at=datetime.now(),
            last_activity=datetime.now()
        )
        
        # Generate personalized greeting
        greeting = random.choice(self.greeting_patterns[time_period])
        starter = random.choice(self.conversation_starters[user_type])
        
        # Add personality touch
        personality_response = get_personality_response('greeting', emotion_analysis['detected_emotion'])
        
        response = {
            'message': f"{greeting}\n\n{starter}",
            'emotion': personality_response['expression'],
            'tone': personality_response['tone'],
            'suggestions': self.get_proactive_suggestions(user_patterns),
            'context': 'greeting',
            'empathy': emotion_analysis.get('empathetic_response', '')
        }
        
        # Record interaction
        advanced_memory.record_interaction(
            'greeting', user_message, emotion_analysis['detected_emotion'], 
            'friendly_greeting', 0.9
        )
        
        return response
    
    def continue_conversation(self, user_message: str, context: str = '') -> Dict[str, Any]:
        """Continue ongoing conversation"""
        if not self.current_context:
            return self.start_conversation(user_message)
        
        # Update context
        self.current_context.last_user_message = user_message
        self.current_context.last_activity = datetime.now()
        
        # Analyze current message
        emotion_analysis = analyze_user_emotion(user_message)
        self.current_context.emotional_state = emotion_analysis['detected_emotion']
        
        # Detect conversation intent
        intent = self.detect_intent(user_message)
        self.current_context.user_intent = intent
        
        # Generate contextual response
        response = self.generate_contextual_response(user_message, emotion_analysis, intent)
        
        # Add to context history
        self.current_context.context_history.append({
            'user_message': user_message,
            'zara_response': response['message'],
            'emotion': emotion_analysis['detected_emotion'],
            'intent': intent,
            'timestamp': datetime.now().isoformat()
        })
        
        # Record interaction
        advanced_memory.record_interaction(
            intent, user_message, emotion_analysis['detected_emotion'], 
            response.get('response_type', 'contextual'), 0.8
        )
        
        return response
    
    def detect_intent(self, message: str) -> str:
        """Detect user intent from message"""
        message_lower = message.lower()
        
        # Intent patterns
        intent_patterns = {
            'question': ['what', 'how', 'why', 'when', 'where', 'which', 'क्या', 'कैसे', 'क्यों', 'कब', 'कहां'],
            'request_help': ['help', 'assist', 'support', 'मदद', 'सहायता', 'can you', 'could you'],
            'task_completion': ['done', 'finished', 'complete', 'हो गया', 'पूरा', 'खत्म'],
            'appreciation': ['thank', 'thanks', 'grateful', 'धन्यवाद', 'शुक्रिया', 'appreciate'],
            'frustration': ['not working', 'problem', 'issue', 'error', 'काम नहीं कर रहा', 'समस्या'],
            'exploration': ['show me', 'tell me about', 'explain', 'बताओ', 'दिखाओ', 'समझाओ'],
            'casual_chat': ['how are you', 'what do you think', 'कैसे हो', 'क्या लगता है']
        }
        
        for intent, patterns in intent_patterns.items():
            if any(pattern in message_lower for pattern in patterns):
                return intent
        
        return 'general'
    
    def generate_contextual_response(self, message: str, emotion_analysis: Dict, intent: str) -> Dict[str, Any]:
        """Generate contextual response based on conversation flow"""
        # Get personality response
        personality_response = get_personality_response(intent, emotion_analysis['detected_emotion'])
        
        # Base response structure
        response = {
            'message': '',
            'emotion': personality_response['expression'],
            'tone': personality_response['tone'],
            'response_type': intent,
            'suggestions': [],
            'empathy': emotion_analysis.get('empathetic_response', '')
        }
        
        # Generate response based on intent
        if intent == 'appreciation':
            response['message'] = random.choice([
                "You're very welcome! I'm happy I could help! 😊",
                "My pleasure! That's what I'm here for! ✨",
                "आपका स्वागत है! मुझे खुशी है कि मैं मदद कर सकी! 🙏"
            ])
            
        elif intent == 'frustration':
            response['message'] = random.choice([
                "I understand that's frustrating. Let's work through this together step by step. 🤝",
                "मैं समझ सकती हूं कि यह परेशान करने वाला है। आइए इसे मिलकर हल करते हैं।",
                "I hear you. Let me help you find a solution that works better. 💪"
            ])
            response['suggestions'] = ["Would you like me to try a different approach?", "Shall we break this down into smaller steps?"]
            
        elif intent == 'question':
            response['message'] = random.choice([
                "Great question! Let me help you with that. 🤔",
                "I'd be happy to explain that for you! 💡",
                "बहुत अच्छा सवाल! मैं इसका जवाब देती हूं।"
            ])
            
        elif intent == 'casual_chat':
            response['message'] = random.choice([
                "I'm doing great, thank you for asking! How are you feeling today? 😊",
                "मैं बहुत अच्छी हूं! आप कैसे हैं?",
                "I'm wonderful! I love our conversations. What's on your mind? ✨"
            ])
            
        else:
            response['message'] = random.choice([
                "I'm here to help! What would you like to work on? 🎯",
                "Let's tackle this together! 💪",
                "मैं आपकी मदद के लिए तैयार हूं!"
            ])
        
        # Add empathetic response if needed
        if emotion_analysis.get('should_be_supportive', False):
            response['message'] = f"{emotion_analysis['empathetic_response']} {response['message']}"
        
        return response
    
    def get_proactive_suggestions(self, user_patterns: Dict) -> List[str]:
        """Generate proactive suggestions based on user patterns"""
        suggestions = []
        
        # Analyze user preferences and patterns
        preferences = user_patterns.get('learned_preferences', {})
        
        if 'work_style' in preferences:
            if preferences['work_style'] == 'productivity':
                suggestions.extend(random.sample(self.proactive_suggestions['productivity'], 1))
        
        if 'interests' in preferences:
            if 'learning' in preferences['interests']:
                suggestions.extend(random.sample(self.proactive_suggestions['learning'], 1))
        
        # Always include wellness suggestions occasionally
        if random.random() < 0.3:  # 30% chance
            suggestions.extend(random.sample(self.proactive_suggestions['wellness'], 1))
        
        return suggestions[:2]  # Limit to 2 suggestions
    
    def end_conversation(self) -> Dict[str, Any]:
        """End current conversation gracefully"""
        if self.current_context:
            # Store conversation summary
            duration = datetime.now() - self.current_context.started_at
            
            # Generate farewell
            farewells = [
                "It was great talking with you! Have a wonderful day! 🌟",
                "Thanks for the lovely conversation! Take care! 😊",
                "बातचीत अच्छी रही! आपका दिन शुभ हो! 🙏",
                "Until next time! Feel free to reach out whenever you need help! ✨"
            ]
            
            farewell = random.choice(farewells)
            
            # Store relationship memory if significant
            if duration.total_seconds() > 300:  # 5+ minutes
                advanced_memory.store_relationship_memory(
                    'conversation_summary',
                    f"Had a {duration.total_seconds()//60:.0f}-minute conversation about {self.current_context.topic}",
                    0.7,
                    self.current_context.emotional_state
                )
            
            # Archive current context
            self.conversation_history.append(self.current_context)
            self.current_context = None
            
            return {
                'message': farewell,
                'emotion': '👋',
                'tone': 'warm',
                'context': 'farewell'
            }
        
        return {'message': 'Goodbye! 👋', 'emotion': '😊', 'tone': 'friendly', 'context': 'farewell'}

# Global instance
conversation_manager = NaturalConversationManager()
