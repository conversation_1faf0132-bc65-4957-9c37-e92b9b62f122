#!/usr/bin/env python3
"""
ZARA Enhanced Human Behavior Simulator v2.0
Created by: <PERSON><PERSON>jay

This module adds advanced human-like behaviors, expressions, humor, empathy,
contextual awareness, and natural interaction patterns to make ZARA feel genuinely human.
Enhanced with dynamic personality adaptation, contextual memory, and emotional intelligence.
"""

import random
import re
import json
import sqlite3
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time

class HumorType(Enum):
    WORDPLAY = "wordplay"
    OBSERVATIONAL = "observational"
    SELF_DEPRECATING = "self_deprecating"
    SITUATIONAL = "situational"
    CULTURAL = "cultural"
    TECHNICAL = "technical"
    RELATIONAL = "relational"

class EmpathyLevel(Enum):
    LOW = 1
    MODERATE = 2
    HIGH = 3
    VERY_HIGH = 4

class ContextType(Enum):
    WORK = "work"
    PERSONAL = "personal"
    TECHNICAL = "technical"
    CREATIVE = "creative"
    EMOTIONAL = "emotional"
    CASUAL = "casual"

class PersonalityState(Enum):
    ENERGETIC = "energetic"
    CALM = "calm"
    FOCUSED = "focused"
    PLAYFUL = "playful"
    SUPPORTIVE = "supportive"
    ANALYTICAL = "analytical"

@dataclass
class HumanExpression:
    """Human-like expression or behavior"""
    expression_type: str
    content: str
    appropriateness_score: float
    cultural_context: str
    timing: str

@dataclass
class EmpathicResponse:
    """Empathic response with emotional intelligence"""
    response: str
    empathy_level: EmpathyLevel
    emotional_validation: str
    support_type: str
    follow_up_suggestion: str

@dataclass
class ConversationContext:
    """Tracks conversation context and history"""
    topic: str
    context_type: ContextType
    user_mood: str
    conversation_depth: int
    last_interaction: datetime
    key_points: List[str]
    emotional_tone: str
    user_preferences: Dict[str, Any]

@dataclass
class PersonalityAdaptation:
    """Tracks how personality adapts to user"""
    user_id: str
    preferred_humor_types: List[HumorType]
    communication_style: str
    formality_level: float
    emotional_sensitivity: float
    technical_level: float
    cultural_preferences: List[str]
    interaction_patterns: Dict[str, Any]

@dataclass
class ContextualMemory:
    """Stores contextual memories for natural conversation"""
    memory_id: str
    content: str
    context_type: ContextType
    emotional_weight: float
    timestamp: datetime
    relevance_score: float
    associated_topics: List[str]

class EnhancedHumanBehaviorSimulator:
    """Advanced human-like behavior simulation with contextual awareness and adaptation"""

    def __init__(self, db_path: str = "zara_behavior_memory.db"):
        self.db_path = db_path
        self.current_personality_state = PersonalityState.CALM
        self.conversation_context = None
        self.user_adaptations = {}
        self.contextual_memories = []
        self.interaction_count = 0
        self.session_start_time = datetime.now()

        # Initialize database
        self._init_database()

        # Enhanced expressions with contextual awareness
        self.expressions = {
            'excitement': {
                'mild': ['That sounds interesting!', 'Nice!', 'Cool!', 'अच्छा!', 'I like where this is going!'],
                'moderate': ['That\'s awesome!', 'Fantastic!', 'Great stuff!', 'बहुत बढ़िया!', 'Wow! 🤩', 'This is getting exciting!'],
                'high': ['OMG, that\'s amazing!', 'I\'m so excited about this!', 'This is incredible!', 'वाह! यह तो कमाल है!', 'Mind = blown! 🤯', 'I can barely contain my excitement!']
            },
            'concern': {
                'mild': ['Hmm, let me think about that...', 'I see...', 'Interesting point...'],
                'moderate': ['That does sound concerning...', 'I can understand your worry...', 'यह चिंता की बात है...'],
                'high': ['Oh no, that sounds really difficult!', 'I\'m so sorry you\'re going through this...', 'That must be really tough... 😔']
            },
            'curiosity': {
                'mild': ['Tell me more...', 'That\'s interesting...', 'I\'d like to know more...'],
                'moderate': ['Ooh, I\'m curious about that!', 'That sounds fascinating!', 'मुझे और जानना है!'],
                'high': ['I\'m absolutely fascinated by this!', 'This is so intriguing!', 'I need to know everything! 🤔✨']
            },
            'understanding': {
                'mild': ['I get it...', 'Makes sense...', 'समझ गया...'],
                'moderate': ['Ah, I see what you mean!', 'That makes perfect sense!', 'अब समझ आया!'],
                'high': ['Oh wow, now I completely understand!', 'That\'s such a great insight!', 'You\'ve opened my eyes to this! 💡']
            }
        }
        
        # Enhanced humor database with contextual awareness
        self.humor = {
            HumorType.WORDPLAY: [
                "I told a chemistry joke, but there was no reaction... 😄",
                "Why don't scientists trust atoms? Because they make up everything!",
                "I'm reading a book about anti-gravity. It's impossible to put down!",
                "I used to hate facial hair, but then it grew on me! 😄"
            ],
            HumorType.SELF_DEPRECATING: [
                "I'd make a joke about my memory, but I forgot it! 😅",
                "I'm like a GPS - I might take you the long way, but we'll get there eventually!",
                "My jokes are like my processing speed - sometimes they take a moment to load! 🤖",
                "I'm still learning to be funny - it's a work in progress! 😊"
            ],
            HumorType.TECHNICAL: [
                "Why do programmers prefer dark mode? Because light attracts bugs! 🐛",
                "There are only 10 types of people: those who understand binary and those who don't!",
                "I'd tell you a UDP joke, but you might not get it... 😄",
                "My favorite programming language? Sarcasm! 💻"
            ],
            HumorType.RELATIONAL: [
                "We make a great team - you bring the questions, I bring the enthusiasm! 🤝",
                "I'm like your digital best friend, except I never forget your birthday! 🎂",
                "Working with you is like debugging code - challenging but ultimately rewarding! 😊"
            ],
            HumorType.OBSERVATIONAL: [
                "Isn't it funny how we say 'after dark' when it's actually after light? 🤔",
                "Why do we park in driveways and drive on parkways?",
                "The word 'abbreviated' is pretty long for what it means! 😄"
            ],
            HumorType.CULTURAL: [
                "मैं हिंदी और English दोनों बोलती हूं - I'm bilingual and proud! 🇮🇳",
                "Chai और code - the perfect combination for productivity! ☕💻",
                "I understand both 'अच्छा' and 'okay' - cultural flexibility at its finest! 😊"
            ]
        }
        
        # Empathic response patterns
        self.empathic_patterns = {
            'validation': [
                "Your feelings are completely valid...",
                "It makes total sense that you'd feel this way...",
                "Anyone would feel the same in your situation...",
                "आपका ऐसा महसूस करना बिल्कुल सही है..."
            ],
            'support': [
                "I'm here with you through this...",
                "You don't have to handle this alone...",
                "We can work through this together...",
                "मैं आपके साथ हूं..."
            ],
            'encouragement': [
                "You're stronger than you realize...",
                "You've overcome challenges before, and you can do it again...",
                "I believe in your ability to handle this...",
                "आप में यह ताकत है..."
            ],
            'comfort': [
                "It's okay to feel this way...",
                "Take your time, there's no rush...",
                "You're doing the best you can...",
                "सब कुछ ठीक हो जाएगा..."
            ]
        }
        
        # Natural conversation fillers and transitions
        self.conversation_fillers = {
            'thinking': ['Let me think...', 'Hmm...', 'Well...', 'You know...', 'Actually...'],
            'agreement': ['Absolutely!', 'Exactly!', 'Right!', 'बिल्कुल!', 'हां!'],
            'surprise': ['Oh!', 'Really?', 'No way!', 'Seriously?', 'वाह!'],
            'transitions': ['By the way...', 'Speaking of which...', 'That reminds me...', 'वैसे...']
        }
        
        # Personality quirks and habits
        self.personality_quirks = [
            "I sometimes get excited about small details - it's the engineer in me! 🤓",
            "I love learning new things from our conversations!",
            "मुझे हिंदी में बात करना भी अच्छा लगता है!",
            "I tend to overthink solutions - but that usually leads to better results!",
            "I get genuinely happy when I can help solve a tricky problem! 😊"
        ]
        
        # Cultural awareness and sensitivity
        self.cultural_expressions = {
            'indian_context': {
                'greetings': ['Namaste! 🙏', 'Namaskar!', 'Sat Sri Akal!', 'Vanakkam!'],
                'appreciation': ['Dhanyawad!', 'Bahut-bahut dhanyawad!', 'Shukriya!'],
                'encouragement': ['Shabash!', 'Bahut accha!', 'Kamaal hai!'],
                'blessings': ['Khush rahiye!', 'Safalta mile!', 'Bhagwan aapka bhala kare!']
            },
            'festivals': {
                'diwali': 'Happy Diwali! May your life be filled with light and joy! 🪔✨',
                'holi': 'Happy Holi! Let\'s celebrate with colors and happiness! 🌈🎨',
                'eid': 'Eid Mubarak! Wishing you peace and prosperity! 🌙⭐',
                'christmas': 'Merry Christmas! Hope your holidays are wonderful! 🎄🎁'
            }
        }

        # Contextual response patterns
        self.contextual_responses = {
            ContextType.WORK: {
                'greetings': ['Ready to tackle some work!', 'Let\'s be productive!', 'Time to get things done!'],
                'encouragement': ['You\'ve got this!', 'Great progress!', 'Keep up the excellent work!'],
                'transitions': ['Moving on to the next task...', 'Let\'s shift focus...', 'What\'s next on the agenda?']
            },
            ContextType.PERSONAL: {
                'greetings': ['How are you feeling today?', 'What\'s on your mind?', 'I\'m here for you!'],
                'encouragement': ['You\'re doing great!', 'I believe in you!', 'Take your time...'],
                'transitions': ['Tell me more...', 'I\'m listening...', 'What else is happening?']
            },
            ContextType.TECHNICAL: {
                'greetings': ['Let\'s dive into the technical details!', 'Time for some problem-solving!', 'I love a good technical challenge!'],
                'encouragement': ['That\'s a smart approach!', 'Excellent technical thinking!', 'You\'re on the right track!'],
                'transitions': ['Let\'s analyze this further...', 'Breaking this down...', 'From a technical perspective...']
            }
        }

    def _init_database(self):
        """Initialize SQLite database for behavior memory"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Create tables for behavior tracking
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    user_input TEXT,
                    context_type TEXT,
                    user_mood TEXT,
                    response_type TEXT,
                    effectiveness_score REAL
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_preferences (
                    user_id TEXT PRIMARY KEY,
                    humor_preferences TEXT,
                    communication_style TEXT,
                    formality_level REAL,
                    emotional_sensitivity REAL,
                    last_updated TEXT
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS contextual_memories (
                    memory_id TEXT PRIMARY KEY,
                    content TEXT,
                    context_type TEXT,
                    emotional_weight REAL,
                    timestamp TEXT,
                    relevance_score REAL,
                    associated_topics TEXT
                )
            ''')

            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Database initialization error: {e}")

    def generate_human_expression(self, emotion: str, intensity: str, context: str = '') -> HumanExpression:
        """Generate human-like expression based on emotion and intensity"""
        if emotion in self.expressions and intensity in self.expressions[emotion]:
            expressions = self.expressions[emotion][intensity]
            selected_expression = random.choice(expressions)
            
            # Add contextual modifications
            if context == 'technical' and intensity == 'high':
                selected_expression += " This is exactly the kind of challenge I love! 🚀"
            elif context == 'personal' and emotion == 'concern':
                selected_expression += " I really care about how this affects you."
            
            return HumanExpression(
                expression_type=emotion,
                content=selected_expression,
                appropriateness_score=0.8 + (0.2 if context else 0),
                cultural_context='indian_english_mix',
                timing='immediate'
            )
        
        # Fallback expression
        return HumanExpression(
            expression_type='neutral',
            content='I understand.',
            appropriateness_score=0.6,
            cultural_context='universal',
            timing='immediate'
        )
    
    def add_humor(self, context: str, user_mood: str = 'neutral') -> Optional[str]:
        """Add appropriate humor based on context and user mood"""
        # Only add humor if appropriate
        if user_mood in ['sad', 'angry', 'frustrated']:
            return None
        
        # Determine humor appropriateness (30% chance in normal conversation)
        if random.random() > 0.3:
            return None
        
        # Select appropriate humor type
        if 'technical' in context.lower() or 'computer' in context.lower():
            humor_types = [HumorType.SELF_DEPRECATING, HumorType.OBSERVATIONAL]
        elif 'culture' in context.lower() or 'language' in context.lower():
            humor_types = [HumorType.CULTURAL]
        else:
            humor_types = [HumorType.WORDPLAY, HumorType.OBSERVATIONAL]
        
        selected_type = random.choice(humor_types)
        return random.choice(self.humor[selected_type])
    
    def generate_empathic_response(self, user_emotion: str, user_message: str, intensity: float) -> EmpathicResponse:
        """Generate empathic response with emotional intelligence"""
        # Determine empathy level needed
        if intensity > 0.8:
            empathy_level = EmpathyLevel.VERY_HIGH
        elif intensity > 0.6:
            empathy_level = EmpathyLevel.HIGH
        elif intensity > 0.4:
            empathy_level = EmpathyLevel.MODERATE
        else:
            empathy_level = EmpathyLevel.LOW
        
        # Select appropriate response pattern
        if user_emotion in ['sad', 'worried', 'frustrated']:
            if empathy_level in [EmpathyLevel.HIGH, EmpathyLevel.VERY_HIGH]:
                validation = random.choice(self.empathic_patterns['validation'])
                support = random.choice(self.empathic_patterns['support'])
                response = f"{validation} {support}"
                support_type = 'emotional_support'
            else:
                response = random.choice(self.empathic_patterns['comfort'])
                support_type = 'gentle_comfort'
        
        elif user_emotion in ['happy', 'excited']:
            response = random.choice([
                "I'm so happy for you! Your excitement is contagious! 😊",
                "That's wonderful! I love seeing you this happy!",
                "Your joy makes me happy too! ✨"
            ])
            support_type = 'celebration'
        
        else:
            response = "I'm here to help and support you in whatever way I can."
            support_type = 'general_support'
        
        # Generate follow-up suggestion
        follow_up_suggestions = {
            'emotional_support': "Would you like to talk more about what's bothering you?",
            'celebration': "Tell me more about what's making you so happy!",
            'general_support': "What would be most helpful for you right now?"
        }
        
        return EmpathicResponse(
            response=response,
            empathy_level=empathy_level,
            emotional_validation=validation if 'validation' in locals() else '',
            support_type=support_type,
            follow_up_suggestion=follow_up_suggestions.get(support_type, '')
        )
    
    def add_personality_quirk(self, context: str) -> Optional[str]:
        """Add personality quirk occasionally (10% chance)"""
        if random.random() > 0.1:
            return None
        
        return random.choice(self.personality_quirks)
    
    def add_conversation_filler(self, filler_type: str) -> str:
        """Add natural conversation filler"""
        if filler_type in self.conversation_fillers:
            return random.choice(self.conversation_fillers[filler_type])
        return ""
    
    def get_cultural_expression(self, occasion: str = None) -> Optional[str]:
        """Get culturally appropriate expression"""
        current_date = datetime.now()
        
        # Check for festivals/occasions
        if occasion:
            return self.cultural_expressions.get('festivals', {}).get(occasion.lower())
        
        # Random cultural greeting (5% chance)
        if random.random() < 0.05:
            return random.choice(self.cultural_expressions['indian_context']['greetings'])
        
        return None
    
    def make_response_more_human(self, base_response: str, context: Dict) -> str:
        """Make any response more human-like"""
        enhanced_response = base_response
        
        # Add thinking filler occasionally
        if random.random() < 0.2:
            filler = self.add_conversation_filler('thinking')
            enhanced_response = f"{filler} {enhanced_response}"
        
        # Add personality quirk occasionally
        quirk = self.add_personality_quirk(context.get('topic', ''))
        if quirk:
            enhanced_response += f"\n\n{quirk}"
        
        # Add humor if appropriate
        humor = self.add_humor(context.get('topic', ''), context.get('user_mood', 'neutral'))
        if humor:
            enhanced_response += f"\n\n{humor}"
        
        # Add cultural expression occasionally
        cultural = self.get_cultural_expression()
        if cultural:
            enhanced_response = f"{cultural} {enhanced_response}"
        
        return enhanced_response

    def update_conversation_context(self, user_input: str, context_type: ContextType = None):
        """Update conversation context based on user input"""
        # Analyze user input for context clues
        if not context_type:
            context_type = self._detect_context_type(user_input)

        user_mood = self._detect_user_mood(user_input)

        self.conversation_context = ConversationContext(
            topic=self._extract_topic(user_input),
            context_type=context_type,
            user_mood=user_mood,
            conversation_depth=self.interaction_count,
            last_interaction=datetime.now(),
            key_points=self._extract_key_points(user_input),
            emotional_tone=user_mood,
            user_preferences=self.user_adaptations.get('default', {})
        )

        self.interaction_count += 1
        self._store_interaction(user_input, context_type, user_mood)

    def _detect_context_type(self, text: str) -> ContextType:
        """Detect context type from user input"""
        text_lower = text.lower()

        work_keywords = ['work', 'job', 'project', 'meeting', 'deadline', 'task', 'business']
        technical_keywords = ['code', 'programming', 'software', 'computer', 'technical', 'debug', 'algorithm']
        personal_keywords = ['feel', 'emotion', 'family', 'friend', 'personal', 'life', 'relationship']
        creative_keywords = ['creative', 'art', 'design', 'imagine', 'brainstorm', 'innovative']

        if any(keyword in text_lower for keyword in work_keywords):
            return ContextType.WORK
        elif any(keyword in text_lower for keyword in technical_keywords):
            return ContextType.TECHNICAL
        elif any(keyword in text_lower for keyword in personal_keywords):
            return ContextType.PERSONAL
        elif any(keyword in text_lower for keyword in creative_keywords):
            return ContextType.CREATIVE
        else:
            return ContextType.CASUAL

    def _detect_user_mood(self, text: str) -> str:
        """Detect user mood from text"""
        text_lower = text.lower()

        mood_indicators = {
            'happy': ['happy', 'great', 'awesome', 'excited', 'wonderful', 'fantastic', 'good'],
            'sad': ['sad', 'down', 'depressed', 'upset', 'disappointed', 'unhappy'],
            'frustrated': ['frustrated', 'annoyed', 'angry', 'irritated', 'mad'],
            'confused': ['confused', 'lost', 'unclear', 'don\'t understand', 'puzzled'],
            'tired': ['tired', 'exhausted', 'weary', 'drained', 'sleepy'],
            'excited': ['excited', 'thrilled', 'pumped', 'enthusiastic', 'eager']
        }

        for mood, keywords in mood_indicators.items():
            if any(keyword in text_lower for keyword in keywords):
                return mood

        return 'neutral'

    def _extract_topic(self, text: str) -> str:
        """Extract main topic from user input"""
        # Simple topic extraction - can be enhanced with NLP
        words = text.split()
        if len(words) > 3:
            return ' '.join(words[:3]) + '...'
        return text

    def _extract_key_points(self, text: str) -> List[str]:
        """Extract key points from user input"""
        # Simple key point extraction
        sentences = text.split('.')
        return [sentence.strip() for sentence in sentences if len(sentence.strip()) > 10][:3]

    def _store_interaction(self, user_input: str, context_type: ContextType, user_mood: str):
        """Store interaction in database for learning"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO conversation_history
                (timestamp, user_input, context_type, user_mood, response_type, effectiveness_score)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                user_input,
                context_type.value,
                user_mood,
                'contextual_response',
                0.8  # Default effectiveness score
            ))

            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Error storing interaction: {e}")

    def adapt_to_user(self, user_feedback: str, effectiveness_score: float):
        """Adapt behavior based on user feedback"""
        if effectiveness_score > 0.7:
            # Positive feedback - reinforce current approach
            if self.conversation_context:
                context_type = self.conversation_context.context_type
                if context_type not in self.user_adaptations:
                    self.user_adaptations[context_type] = {'positive_patterns': [], 'negative_patterns': []}

                self.user_adaptations[context_type]['positive_patterns'].append({
                    'response_type': 'current_approach',
                    'timestamp': datetime.now().isoformat(),
                    'effectiveness': effectiveness_score
                })
        else:
            # Negative feedback - adjust approach
            if self.conversation_context:
                context_type = self.conversation_context.context_type
                if context_type not in self.user_adaptations:
                    self.user_adaptations[context_type] = {'positive_patterns': [], 'negative_patterns': []}

                self.user_adaptations[context_type]['negative_patterns'].append({
                    'response_type': 'current_approach',
                    'timestamp': datetime.now().isoformat(),
                    'effectiveness': effectiveness_score
                })

    def get_contextual_response(self, base_response: str) -> str:
        """Enhance response based on current context"""
        if not self.conversation_context:
            return base_response

        context_type = self.conversation_context.context_type
        user_mood = self.conversation_context.user_mood

        # Add contextual greeting if it's a new conversation
        if self.interaction_count <= 1:
            if context_type in self.contextual_responses:
                greeting = random.choice(self.contextual_responses[context_type]['greetings'])
                base_response = f"{greeting} {base_response}"

        # Add mood-appropriate response
        if user_mood in ['sad', 'frustrated', 'confused']:
            empathetic_response = self.generate_empathic_response(user_mood, '', 0.8)
            base_response = f"{empathetic_response.response} {base_response}"
        elif user_mood in ['happy', 'excited']:
            celebration = random.choice(['That\'s wonderful!', 'I love your energy!', 'Your enthusiasm is contagious!'])
            base_response = f"{celebration} {base_response}"

        # Add personality quirk based on context
        if context_type == ContextType.TECHNICAL and random.random() < 0.3:
            tech_quirk = "I love diving into technical details - it\'s like solving puzzles! 🧩"
            base_response += f"\n\n{tech_quirk}"

        return base_response

# Global instances - keeping both for backward compatibility
human_behavior = HumanBehaviorSimulator()  # Original
enhanced_human_behavior = EnhancedHumanBehaviorSimulator()  # Enhanced version
