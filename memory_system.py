"""
ZARA Memory System - Long-term Conversation Storage
Created by: <PERSON><PERSON>

This module handles all memory operations for ZARA including:
- Conversation history storage
- User preferences
- Reminders and tasks
- Learning from interactions
- Context retrieval
"""

import sqlite3
import json
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import logging
import os
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ZARAMemorySystem:
    def __init__(self, db_path: str = "zara_memory/zara_memory.db"):
        """Initialize ZARA Memory System"""
        self.db_path = db_path
        self.ensure_directory_exists()
        self.init_database()
        
    def ensure_directory_exists(self):
        """Create memory directory if it doesn't exist"""
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
    def init_database(self):
        """Initialize all database tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Conversations table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS conversations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        user_message TEXT,
                        assistant_response TEXT,
                        tools_used TEXT,  -- JSON array of tools used
                        context_tags TEXT,  -- JSON array of context tags
                        sentiment REAL,  -- Sentiment score (-1 to 1)
                        importance_score INTEGER DEFAULT 1,  -- 1-10 scale
                        metadata TEXT  -- JSON for additional data
                    )
                ''')
                
                # User preferences table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_preferences (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        preference_key TEXT UNIQUE NOT NULL,
                        preference_value TEXT NOT NULL,
                        category TEXT DEFAULT 'general',
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT
                    )
                ''')
                
                # Reminders table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS reminders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        reminder_date DATE NOT NULL,
                        reminder_time TIME,
                        title TEXT NOT NULL,
                        description TEXT,
                        status TEXT DEFAULT 'active',  -- active, completed, cancelled
                        priority INTEGER DEFAULT 1,  -- 1-5 scale
                        recurring TEXT,  -- daily, weekly, monthly, yearly
                        metadata TEXT
                    )
                ''')
                
                # Learning patterns table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS learning_patterns (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        pattern_type TEXT NOT NULL,  -- command, preference, behavior
                        pattern_data TEXT NOT NULL,  -- JSON data
                        frequency INTEGER DEFAULT 1,
                        last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
                        confidence_score REAL DEFAULT 0.5,
                        metadata TEXT
                    )
                ''')
                
                # Context memory table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS context_memory (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        context_type TEXT NOT NULL,  -- topic, location, time, person
                        context_value TEXT NOT NULL,
                        related_conversations TEXT,  -- JSON array of conversation IDs
                        importance_score INTEGER DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        last_accessed DATETIME DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT
                    )
                ''')
                
                # Chat messages table (for compatibility)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS chat_messages (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        role TEXT NOT NULL,  -- user, assistant, system
                        content TEXT NOT NULL,
                        session_id TEXT,
                        metadata TEXT
                    )
                ''')
                
                conn.commit()
                logger.info(" ZARA Memory System initialized successfully")
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize database: {e}")
            raise

    async def store_conversation(self, 
                                session_id: str,
                                user_message: str,
                                assistant_response: str,
                                tools_used: List[str] = None,
                                context_tags: List[str] = None,
                                importance_score: int = 1) -> int:
        """Store a complete conversation exchange"""
        try:
            tools_json = json.dumps(tools_used or [])
            tags_json = json.dumps(context_tags or [])
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO conversations 
                    (session_id, user_message, assistant_response, tools_used, context_tags, importance_score)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (session_id, user_message, assistant_response, tools_json, tags_json, importance_score))
                
                conversation_id = cursor.lastrowid
                
                # Also store in chat_messages for compatibility
                cursor.execute('''
                    INSERT INTO chat_messages (role, content, session_id)
                    VALUES (?, ?, ?)
                ''', ('user', user_message, session_id))
                
                cursor.execute('''
                    INSERT INTO chat_messages (role, content, session_id)
                    VALUES (?, ?, ?)
                ''', ('assistant', assistant_response, session_id))
                
                conn.commit()
                logger.info(f"✅ Stored conversation {conversation_id}")
                return conversation_id
                
        except Exception as e:
            logger.error(f"❌ Failed to store conversation: {e}")
            return -1

    async def get_recent_conversations(self, limit: int = 10, session_id: str = None) -> List[Dict]:
        """Retrieve recent conversations"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if session_id:
                    cursor.execute('''
                        SELECT * FROM conversations 
                        WHERE session_id = ?
                        ORDER BY timestamp DESC 
                        LIMIT ?
                    ''', (session_id, limit))
                else:
                    cursor.execute('''
                        SELECT * FROM conversations 
                        ORDER BY timestamp DESC 
                        LIMIT ?
                    ''', (limit,))
                
                columns = [desc[0] for desc in cursor.description]
                conversations = []
                
                for row in cursor.fetchall():
                    conv = dict(zip(columns, row))
                    # Parse JSON fields
                    conv['tools_used'] = json.loads(conv['tools_used'] or '[]')
                    conv['context_tags'] = json.loads(conv['context_tags'] or '[]')
                    conversations.append(conv)
                
                return conversations
                
        except Exception as e:
            logger.error(f"❌ Failed to retrieve conversations: {e}")
            return []

    async def store_user_preference(self, key: str, value: str, category: str = 'general') -> bool:
        """Store or update user preference"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO user_preferences 
                    (preference_key, preference_value, category, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                ''', (key, value, category))
                
                conn.commit()
                logger.info(f"✅ Stored preference: {key} = {value}")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to store preference: {e}")
            return False

    async def get_user_preference(self, key: str) -> Optional[str]:
        """Retrieve user preference"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT preference_value FROM user_preferences 
                    WHERE preference_key = ?
                ''', (key,))
                
                result = cursor.fetchone()
                return result[0] if result else None
                
        except Exception as e:
            logger.error(f"❌ Failed to retrieve preference: {e}")
            return None

    async def add_reminder(self, 
                          title: str,
                          reminder_date: str,
                          reminder_time: str = None,
                          description: str = None,
                          priority: int = 1) -> int:
        """Add a new reminder"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO reminders 
                    (title, description, reminder_date, reminder_time, priority)
                    VALUES (?, ?, ?, ?, ?)
                ''', (title, description, reminder_date, reminder_time, priority))
                
                reminder_id = cursor.lastrowid
                conn.commit()
                logger.info(f"✅ Added reminder {reminder_id}: {title}")
                return reminder_id
                
        except Exception as e:
            logger.error(f"❌ Failed to add reminder: {e}")
            return -1

    async def get_todays_reminders(self) -> List[Dict]:
        """Get all reminders for today"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM reminders
                    WHERE reminder_date = ? AND status = 'active'
                    ORDER BY reminder_time ASC
                ''', (today,))

                columns = [desc[0] for desc in cursor.description]
                reminders = []

                for row in cursor.fetchall():
                    reminder = dict(zip(columns, row))
                    reminders.append(reminder)

                return reminders

        except Exception as e:
            logger.error(f"❌ Failed to get today's reminders: {e}")
            return []

    async def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:
        """Search conversations by content"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM conversations
                    WHERE user_message LIKE ? OR assistant_response LIKE ?
                    ORDER BY importance_score DESC, timestamp DESC
                    LIMIT ?
                ''', (f'%{query}%', f'%{query}%', limit))

                columns = [desc[0] for desc in cursor.description]
                conversations = []

                for row in cursor.fetchall():
                    conv = dict(zip(columns, row))
                    conv['tools_used'] = json.loads(conv['tools_used'] or '[]')
                    conv['context_tags'] = json.loads(conv['context_tags'] or '[]')
                    conversations.append(conv)

                return conversations

        except Exception as e:
            logger.error(f"❌ Failed to search conversations: {e}")
            return []

    async def learn_pattern(self, pattern_type: str, pattern_data: Dict, frequency: int = 1) -> bool:
        """Store learning patterns for AI improvement"""
        try:
            pattern_json = json.dumps(pattern_data)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Check if pattern exists
                cursor.execute('''
                    SELECT id, frequency FROM learning_patterns
                    WHERE pattern_type = ? AND pattern_data = ?
                ''', (pattern_type, pattern_json))

                existing = cursor.fetchone()

                if existing:
                    # Update frequency
                    cursor.execute('''
                        UPDATE learning_patterns
                        SET frequency = frequency + ?, last_used = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ''', (frequency, existing[0]))
                else:
                    # Insert new pattern
                    cursor.execute('''
                        INSERT INTO learning_patterns
                        (pattern_type, pattern_data, frequency)
                        VALUES (?, ?, ?)
                    ''', (pattern_type, pattern_json, frequency))

                conn.commit()
                logger.info(f"✅ Learned pattern: {pattern_type}")
                return True

        except Exception as e:
            logger.error(f"❌ Failed to learn pattern: {e}")
            return False

    async def get_context_summary(self, days: int = 7) -> str:
        """Generate context summary for recent conversations"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S')

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Get recent conversations
                cursor.execute('''
                    SELECT user_message, assistant_response, tools_used, timestamp
                    FROM conversations
                    WHERE timestamp > ?
                    ORDER BY importance_score DESC, timestamp DESC
                    LIMIT 20
                ''', (cutoff_date,))

                conversations = cursor.fetchall()

                if not conversations:
                    return "कोई हाल की बातचीत नहीं मिली।"

                # Generate summary
                summary_parts = []
                summary_parts.append(f"पिछले {days} दिनों में {len(conversations)} बातचीत हुई हैं।")

                # Most used tools
                all_tools = []
                for conv in conversations:
                    tools = json.loads(conv[2] or '[]')
                    all_tools.extend(tools)

                if all_tools:
                    from collections import Counter
                    tool_counts = Counter(all_tools)
                    top_tools = tool_counts.most_common(3)
                    tools_text = ", ".join([f"{tool} ({count}x)" for tool, count in top_tools])
                    summary_parts.append(f"सबसे ज्यादा इस्तेमाल किए गए टूल्स: {tools_text}")

                # Recent topics
                recent_topics = []
                for conv in conversations[:5]:
                    if conv[0]:  # user_message
                        words = conv[0].split()[:5]  # First 5 words
                        recent_topics.append(" ".join(words))

                if recent_topics:
                    summary_parts.append(f"हाल के विषय: {', '.join(recent_topics[:3])}")

                return "\n".join(summary_parts)

        except Exception as e:
            logger.error(f"❌ Failed to generate context summary: {e}")
            return "संदर्भ सारांश बनाने में त्रुटि।"

    async def cleanup_old_data(self, days_to_keep: int = 90) -> bool:
        """Clean up old conversation data"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).strftime('%Y-%m-%d %H:%M:%S')

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Delete old conversations with low importance
                cursor.execute('''
                    DELETE FROM conversations
                    WHERE timestamp < ? AND importance_score < 3
                ''', (cutoff_date,))

                deleted_conversations = cursor.rowcount

                # Delete old chat messages
                cursor.execute('''
                    DELETE FROM chat_messages
                    WHERE timestamp < ?
                ''', (cutoff_date,))

                deleted_messages = cursor.rowcount

                # Delete completed reminders older than 30 days
                old_reminder_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                cursor.execute('''
                    DELETE FROM reminders
                    WHERE reminder_date < ? AND status = 'completed'
                ''', (old_reminder_date,))

                deleted_reminders = cursor.rowcount

                conn.commit()

                logger.info(f"✅ Cleanup completed: {deleted_conversations} conversations, {deleted_messages} messages, {deleted_reminders} reminders deleted")
                return True

        except Exception as e:
            logger.error(f"❌ Failed to cleanup old data: {e}")
            return False

    async def get_memory_stats(self) -> Dict:
        """Get memory system statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                stats = {}

                # Conversation count
                cursor.execute('SELECT COUNT(*) FROM conversations')
                stats['total_conversations'] = cursor.fetchone()[0]

                # Today's conversations
                today = datetime.now().strftime('%Y-%m-%d')
                cursor.execute('SELECT COUNT(*) FROM conversations WHERE DATE(timestamp) = ?', (today,))
                stats['todays_conversations'] = cursor.fetchone()[0]

                # User preferences count
                cursor.execute('SELECT COUNT(*) FROM user_preferences')
                stats['user_preferences'] = cursor.fetchone()[0]

                # Active reminders
                cursor.execute('SELECT COUNT(*) FROM reminders WHERE status = "active"')
                stats['active_reminders'] = cursor.fetchone()[0]

                # Learning patterns
                cursor.execute('SELECT COUNT(*) FROM learning_patterns')
                stats['learning_patterns'] = cursor.fetchone()[0]

                # Database size
                stats['db_size_mb'] = round(os.path.getsize(self.db_path) / (1024 * 1024), 2)

                return stats

        except Exception as e:
            logger.error(f"❌ Failed to get memory stats: {e}")
            return {}

# Global memory instance
memory_system = ZARAMemorySystem()

# Convenience functions for easy access
async def store_conversation(session_id: str, user_msg: str, assistant_msg: str, tools: List[str] = None):
    """Quick function to store conversation"""
    return await memory_system.store_conversation(session_id, user_msg, assistant_msg, tools)

async def get_recent_context(limit: int = 5):
    """Quick function to get recent context"""
    return await memory_system.get_recent_conversations(limit)

async def remember_preference(key: str, value: str):
    """Quick function to remember user preference"""
    return await memory_system.store_user_preference(key, value)

async def recall_preference(key: str):
    """Quick function to recall user preference"""
    return await memory_system.get_user_preference(key)

async def add_reminder(title: str, date: str, time: str = None, description: str = None):
    """Quick function to add reminder"""
    return await memory_system.add_reminder(title, date, time, description)

async def get_todays_reminders():
    """Quick function to get today's reminders"""
    return await memory_system.get_todays_reminders()
