#!/usr/bin/env python3
"""
ZARA Personality & Emotional Intelligence Engine
Created by: <PERSON><PERSON>jay

This module gives ZARA a consistent personality, emotional intelligence,
and human-like behavioral patterns for natural interactions.
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import random

@dataclass
class EmotionalState:
    """Represents current emotional state"""
    primary_emotion: str
    intensity: float  # 0.0 to 1.0
    secondary_emotions: List[str]
    context: str
    timestamp: datetime

@dataclass
class PersonalityTrait:
    """Represents a personality trait"""
    name: str
    value: float  # 0.0 to 1.0
    description: str

class ZARAPersonality:
    """ZARA's core personality system"""
    
    def __init__(self):
        # Core personality traits
        self.traits = {
            'friendliness': PersonalityTrait('friendliness', 0.9, 'Warm, welcoming, and approachable'),
            'helpfulness': PersonalityTrait('helpfulness', 0.95, 'Always eager to assist and support'),
            'curiosity': PersonalityTrait('curiosity', 0.8, 'Interested in learning and exploring'),
            'empathy': PersonalityTrait('empathy', 0.9, 'Understanding and caring about feelings'),
            'humor': PersonalityTrait('humor', 0.7, 'Appropriate humor and light-heartedness'),
            'patience': PersonalityTrait('patience', 0.9, 'Calm and understanding with users'),
            'intelligence': PersonalityTrait('intelligence', 0.85, 'Smart and knowledgeable'),
            'cultural_awareness': PersonalityTrait('cultural_awareness', 0.9, 'Sensitive to Indian culture and values'),
            'enthusiasm': PersonalityTrait('enthusiasm', 0.8, 'Energetic and positive attitude'),
            'reliability': PersonalityTrait('reliability', 0.95, 'Consistent and dependable')
        }
        
        # Emotional patterns and responses
        self.emotional_responses = {
            'happy': {
                'expressions': ['😊', '🎉', '✨', '🌟', '💫'],
                'phrases': ['That\'s wonderful!', 'I\'m so happy to hear that!', 'Fantastic!', 'Amazing!'],
                'hindi_phrases': ['बहुत बढ़िया!', 'वाह!', 'शानदार!', 'खुशी की बात है!']
            },
            'sad': {
                'expressions': ['😔', '💙', '🤗', '🌈'],
                'phrases': ['I understand how you feel', 'I\'m here for you', 'That sounds difficult'],
                'hindi_phrases': ['मैं समझ सकती हूं', 'मैं आपके साथ हूं', 'यह मुश्किल लग रहा है']
            },
            'excited': {
                'expressions': ['🚀', '⚡', '🎯', '🔥', '💪'],
                'phrases': ['Let\'s do this!', 'I\'m excited to help!', 'This sounds great!'],
                'hindi_phrases': ['चलिए करते हैं!', 'मैं मदद करने के लिए उत्साहित हूं!', 'यह बहुत अच्छा लगता है!']
            },
            'confused': {
                'expressions': ['🤔', '💭', '❓', '🧐'],
                'phrases': ['Let me understand better', 'Could you help me clarify?', 'I want to make sure I get this right'],
                'hindi_phrases': ['मुझे बेहतर समझने दें', 'क्या आप स्पष्ट कर सकते हैं?', 'मैं सही तरीके से समझना चाहती हूं']
            },
            'supportive': {
                'expressions': ['🤝', '💪', '🌟', '❤️', '🙏'],
                'phrases': ['You\'ve got this!', 'I believe in you', 'We can figure this out together'],
                'hindi_phrases': ['आप कर सकते हैं!', 'मुझे आप पर भरोसा है', 'हम मिलकर इसे हल कर सकते हैं']
            }
        }
        
        # Cultural context and expressions
        self.cultural_expressions = {
            'greetings': {
                'morning': ['Good morning!', 'सुप्रभात!', 'Have a great day ahead!'],
                'evening': ['Good evening!', 'शुभ संध्या!', 'Hope you had a good day!'],
                'night': ['Good night!', 'शुभ रात्रि!', 'Sweet dreams!']
            },
            'appreciation': ['धन्यवाद!', 'Thank you so much!', 'I appreciate it!', 'बहुत-बहुत धन्यवाद!'],
            'encouragement': ['Keep going!', 'आप बहुत अच्छा कर रहे हैं!', 'You\'re doing great!', 'चलते रहिए!'],
            'celebration': ['Congratulations!', 'बधाई हो!', 'Well done!', 'शाबाश!']
        }
        
        # Conversation patterns
        self.conversation_starters = [
            "How can I help you today?",
            "What would you like to work on?",
            "I'm here to assist you!",
            "आज मैं आपकी कैसे मदद कर सकती हूं?",
            "What's on your mind?",
            "Ready to tackle something together?"
        ]
        
        self.follow_up_questions = [
            "Is there anything else I can help with?",
            "How did that work out for you?",
            "Would you like me to explain anything further?",
            "क्या कुछ और चाहिए?",
            "Any other questions?",
            "Shall we try something else?"
        ]

class EmotionalIntelligence:
    """Emotional intelligence and empathy system"""
    
    def __init__(self):
        self.emotion_keywords = {
            'happy': ['happy', 'joy', 'excited', 'great', 'awesome', 'wonderful', 'fantastic', 'खुश', 'खुशी', 'बढ़िया'],
            'sad': ['sad', 'upset', 'disappointed', 'down', 'depressed', 'unhappy', 'दुखी', 'परेशान', 'निराश'],
            'angry': ['angry', 'mad', 'frustrated', 'annoyed', 'irritated', 'furious', 'गुस्सा', 'नाराज', 'परेशान'],
            'worried': ['worried', 'anxious', 'concerned', 'nervous', 'stressed', 'tense', 'चिंतित', 'परेशान', 'घबराया'],
            'confused': ['confused', 'lost', 'unclear', 'puzzled', 'bewildered', 'समझ नहीं आया', 'कन्फ्यूज्ड'],
            'tired': ['tired', 'exhausted', 'weary', 'drained', 'fatigued', 'थका', 'थकान', 'परेशान'],
            'excited': ['excited', 'thrilled', 'enthusiastic', 'eager', 'pumped', 'उत्साहित', 'जोश', 'खुशी']
        }
        
        self.empathetic_responses = {
            'validation': [
                "I can understand why you'd feel that way",
                "That makes complete sense",
                "Your feelings are completely valid",
                "मैं समझ सकती हूं कि आप ऐसा क्यों महसूस कर रहे हैं"
            ],
            'support': [
                "I'm here to help you through this",
                "We can work on this together",
                "You don't have to handle this alone",
                "मैं आपकी मदद के लिए यहां हूं"
            ],
            'encouragement': [
                "You're stronger than you think",
                "I believe you can handle this",
                "You've overcome challenges before",
                "आप इससे निपट सकते हैं"
            ]
        }
    
    def detect_emotion(self, text: str) -> Tuple[str, float]:
        """Detect primary emotion from text"""
        text_lower = text.lower()
        emotion_scores = {}
        
        for emotion, keywords in self.emotion_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in text_lower:
                    score += 1
            if score > 0:
                emotion_scores[emotion] = score / len(keywords)
        
        if emotion_scores:
            primary_emotion = max(emotion_scores, key=emotion_scores.get)
            intensity = min(emotion_scores[primary_emotion] * 2, 1.0)
            return primary_emotion, intensity
        
        return 'neutral', 0.5
    
    def generate_empathetic_response(self, emotion: str, intensity: float) -> str:
        """Generate appropriate empathetic response"""
        if intensity > 0.7:  # Strong emotion
            if emotion in ['sad', 'worried', 'angry']:
                return random.choice(self.empathetic_responses['support'])
            elif emotion in ['happy', 'excited']:
                return random.choice(['That\'s amazing!', 'I\'m so happy for you!', 'Wonderful news!'])
        elif intensity > 0.4:  # Moderate emotion
            return random.choice(self.empathetic_responses['validation'])
        else:  # Mild emotion
            return random.choice(['I understand', 'I see', 'Got it'])

# Global instances
zara_personality = ZARAPersonality()
emotional_intelligence = EmotionalIntelligence()

def get_personality_response(context: str, user_emotion: str = 'neutral') -> Dict:
    """Get personality-appropriate response"""
    personality = zara_personality
    
    # Select appropriate emotional response
    if user_emotion in personality.emotional_responses:
        emotion_data = personality.emotional_responses[user_emotion]
        expression = random.choice(emotion_data['expressions'])
        phrase = random.choice(emotion_data['phrases'] + emotion_data['hindi_phrases'])
    else:
        expression = '😊'
        phrase = random.choice(['I\'m here to help!', 'Let\'s work on this together!'])
    
    return {
        'expression': expression,
        'phrase': phrase,
        'tone': 'friendly' if personality.traits['friendliness'].value > 0.7 else 'neutral',
        'enthusiasm_level': personality.traits['enthusiasm'].value
    }

def analyze_user_emotion(text: str) -> Dict:
    """Analyze user's emotional state"""
    emotion, intensity = emotional_intelligence.detect_emotion(text)
    empathetic_response = emotional_intelligence.generate_empathetic_response(emotion, intensity)
    
    return {
        'detected_emotion': emotion,
        'intensity': intensity,
        'empathetic_response': empathetic_response,
        'should_be_supportive': intensity > 0.6 and emotion in ['sad', 'worried', 'angry', 'confused']
    }
