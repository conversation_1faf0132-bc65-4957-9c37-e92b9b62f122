#!/usr/bin/env python3
"""
ZARA Advanced Memory & Relationship System
Created by: <PERSON><PERSON> Sanjay

This module creates deep relationship memory, preference learning,
and personalized experience building for human-like interactions.
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import logging

@dataclass
class UserPreference:
    """User preference data"""
    category: str
    preference: str
    value: Any
    confidence: float
    learned_date: datetime
    last_confirmed: datetime

@dataclass
class ConversationPattern:
    """User conversation patterns"""
    pattern_type: str
    pattern_data: Dict
    frequency: int
    last_seen: datetime

@dataclass
class RelationshipMemory:
    """Relationship and personal memory"""
    memory_type: str
    content: str
    importance: float
    emotional_context: str
    created_date: datetime
    last_referenced: datetime

class AdvancedMemorySystem:
    """Enhanced memory system for relationship building"""
    
    def __init__(self, db_path: str = "zara_advanced_memory.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.init_database()
        
        # Learning patterns
        self.preference_categories = {
            'communication_style': ['formal', 'casual', 'mixed', 'hindi_preferred', 'english_preferred'],
            'task_preferences': ['step_by_step', 'quick_summary', 'detailed_explanation'],
            'time_patterns': ['morning_person', 'night_owl', 'flexible'],
            'work_style': ['focused', 'multitasker', 'methodical', 'creative'],
            'interests': ['technology', 'productivity', 'entertainment', 'learning', 'health'],
            'emotional_needs': ['encouragement', 'validation', 'practical_help', 'companionship']
        }
    
    def init_database(self):
        """Initialize advanced memory database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # User preferences table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_preferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category TEXT NOT NULL,
                    preference TEXT NOT NULL,
                    value TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    learned_date TEXT NOT NULL,
                    last_confirmed TEXT NOT NULL
                )
            ''')
            
            # Conversation patterns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern_type TEXT NOT NULL,
                    pattern_data TEXT NOT NULL,
                    frequency INTEGER NOT NULL,
                    last_seen TEXT NOT NULL
                )
            ''')
            
            # Relationship memories table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS relationship_memories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    memory_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    importance REAL NOT NULL,
                    emotional_context TEXT NOT NULL,
                    created_date TEXT NOT NULL,
                    last_referenced TEXT NOT NULL
                )
            ''')
            
            # User interaction history
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS interaction_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    interaction_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    user_emotion TEXT,
                    zara_response_type TEXT,
                    success_rating REAL,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            # Personal context table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS personal_context (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    context_type TEXT NOT NULL,
                    context_data TEXT NOT NULL,
                    relevance_score REAL NOT NULL,
                    created_date TEXT NOT NULL,
                    last_updated TEXT NOT NULL
                )
            ''')
            
            conn.commit()
            conn.close()
            self.logger.info("Advanced memory database initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize advanced memory database: {e}")
    
    def learn_user_preference(self, category: str, preference: str, value: Any, confidence: float = 0.7):
        """Learn and store user preference"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if preference already exists
            cursor.execute('''
                SELECT id, confidence FROM user_preferences 
                WHERE category = ? AND preference = ?
            ''', (category, preference))
            
            existing = cursor.fetchone()
            current_time = datetime.now().isoformat()
            
            if existing:
                # Update existing preference with weighted confidence
                old_confidence = existing[1]
                new_confidence = (old_confidence + confidence) / 2
                
                cursor.execute('''
                    UPDATE user_preferences 
                    SET value = ?, confidence = ?, last_confirmed = ?
                    WHERE id = ?
                ''', (json.dumps(value), new_confidence, current_time, existing[0]))
            else:
                # Insert new preference
                cursor.execute('''
                    INSERT INTO user_preferences 
                    (category, preference, value, confidence, learned_date, last_confirmed)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (category, preference, json.dumps(value), confidence, current_time, current_time))
            
            conn.commit()
            conn.close()
            self.logger.info(f"Learned user preference: {category} - {preference}")
            
        except Exception as e:
            self.logger.error(f"Failed to learn user preference: {e}")
    
    def get_user_preferences(self, category: str = None) -> List[UserPreference]:
        """Get user preferences"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if category:
                cursor.execute('''
                    SELECT category, preference, value, confidence, learned_date, last_confirmed
                    FROM user_preferences WHERE category = ?
                    ORDER BY confidence DESC
                ''', (category,))
            else:
                cursor.execute('''
                    SELECT category, preference, value, confidence, learned_date, last_confirmed
                    FROM user_preferences ORDER BY confidence DESC
                ''')
            
            preferences = []
            for row in cursor.fetchall():
                preferences.append(UserPreference(
                    category=row[0],
                    preference=row[1],
                    value=json.loads(row[2]),
                    confidence=row[3],
                    learned_date=datetime.fromisoformat(row[4]),
                    last_confirmed=datetime.fromisoformat(row[5])
                ))
            
            conn.close()
            return preferences
            
        except Exception as e:
            self.logger.error(f"Failed to get user preferences: {e}")
            return []
    
    def store_relationship_memory(self, memory_type: str, content: str, importance: float, emotional_context: str):
        """Store important relationship memory"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().isoformat()
            
            cursor.execute('''
                INSERT INTO relationship_memories 
                (memory_type, content, importance, emotional_context, created_date, last_referenced)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (memory_type, content, importance, emotional_context, current_time, current_time))
            
            conn.commit()
            conn.close()
            self.logger.info(f"Stored relationship memory: {memory_type}")
            
        except Exception as e:
            self.logger.error(f"Failed to store relationship memory: {e}")
    
    def get_relevant_memories(self, context: str, limit: int = 5) -> List[RelationshipMemory]:
        """Get relevant memories for current context"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Simple keyword matching for now (can be enhanced with embeddings)
            keywords = context.lower().split()
            
            cursor.execute('''
                SELECT memory_type, content, importance, emotional_context, created_date, last_referenced
                FROM relationship_memories 
                WHERE importance > 0.5
                ORDER BY importance DESC, created_date DESC
                LIMIT ?
            ''', (limit,))
            
            memories = []
            for row in cursor.fetchall():
                memories.append(RelationshipMemory(
                    memory_type=row[0],
                    content=row[1],
                    importance=row[2],
                    emotional_context=row[3],
                    created_date=datetime.fromisoformat(row[4]),
                    last_referenced=datetime.fromisoformat(row[5])
                ))
            
            conn.close()
            return memories
            
        except Exception as e:
            self.logger.error(f"Failed to get relevant memories: {e}")
            return []
    
    def record_interaction(self, interaction_type: str, content: str, user_emotion: str, 
                          zara_response_type: str, success_rating: float = 0.8):
        """Record interaction for learning"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO interaction_history 
                (interaction_type, content, user_emotion, zara_response_type, success_rating, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (interaction_type, content, user_emotion, zara_response_type, success_rating, 
                  datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Failed to record interaction: {e}")
    
    def analyze_user_patterns(self) -> Dict[str, Any]:
        """Analyze user patterns and preferences"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Analyze communication patterns
            cursor.execute('''
                SELECT user_emotion, COUNT(*) as count
                FROM interaction_history 
                WHERE timestamp > datetime('now', '-30 days')
                GROUP BY user_emotion
                ORDER BY count DESC
            ''')
            
            emotion_patterns = dict(cursor.fetchall())
            
            # Analyze success patterns
            cursor.execute('''
                SELECT zara_response_type, AVG(success_rating) as avg_success
                FROM interaction_history 
                WHERE timestamp > datetime('now', '-30 days')
                GROUP BY zara_response_type
                ORDER BY avg_success DESC
            ''')
            
            success_patterns = dict(cursor.fetchall())
            
            # Get recent preferences
            preferences = self.get_user_preferences()
            
            conn.close()
            
            return {
                'emotion_patterns': emotion_patterns,
                'successful_response_types': success_patterns,
                'learned_preferences': {p.category: p.value for p in preferences if p.confidence > 0.6},
                'relationship_strength': self.calculate_relationship_strength()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to analyze user patterns: {e}")
            return {}
    
    def calculate_relationship_strength(self) -> float:
        """Calculate relationship strength based on interactions"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Count total interactions
            cursor.execute('SELECT COUNT(*) FROM interaction_history')
            total_interactions = cursor.fetchone()[0]
            
            # Count recent interactions
            cursor.execute('''
                SELECT COUNT(*) FROM interaction_history 
                WHERE timestamp > datetime('now', '-7 days')
            ''')
            recent_interactions = cursor.fetchone()[0]
            
            # Average success rating
            cursor.execute('SELECT AVG(success_rating) FROM interaction_history')
            avg_success = cursor.fetchone()[0] or 0.5
            
            # Count relationship memories
            cursor.execute('SELECT COUNT(*) FROM relationship_memories')
            memory_count = cursor.fetchone()[0]
            
            conn.close()
            
            # Calculate relationship strength (0.0 to 1.0)
            interaction_factor = min(total_interactions / 100, 1.0)  # Max at 100 interactions
            recency_factor = min(recent_interactions / 10, 1.0)      # Max at 10 recent interactions
            success_factor = avg_success
            memory_factor = min(memory_count / 20, 1.0)              # Max at 20 memories
            
            relationship_strength = (interaction_factor * 0.3 + recency_factor * 0.2 + 
                                   success_factor * 0.3 + memory_factor * 0.2)
            
            return relationship_strength
            
        except Exception as e:
            self.logger.error(f"Failed to calculate relationship strength: {e}")
            return 0.5

# Global instance
advanced_memory = AdvancedMemorySystem()
