# ZARA Voice Assistant Dependencies
# Core LiveKit Framework
livekit-agents>=1.1.5
livekit>=1.0.11
livekit-api>=1.0.3
livekit-protocol>=1.0.4

# LiveKit Plugins
livekit-plugins-google>=1.1.5
livekit-plugins-openai>=1.1.5
livekit-plugins-silero>=1.1.5
livekit-plugins-noise-cancellation>=0.2.5

# Core Dependencies
python-dotenv>=1.0.0
asyncio-mqtt>=0.16.0
aiohttp>=3.9.0
requests>=2.32.0

# AI/ML Libraries
torch>=2.0.0
transformers>=4.30.0
numpy>=1.24.0
pandas>=2.0.0
matplotlib>=3.7.0
scikit-learn>=1.3.0

# GUI and Automation
PyQt5>=5.15.0
PyQt6>=6.4.0
pyautogui>=0.9.54
pygetwindow>=0.0.9
opencv-python>=4.8.0
pytesseract>=0.3.10
Pillow>=10.0.0

# System and Utilities
psutil>=5.9.0
subprocess32>=3.5.4
ctypes-callable>=1.0.0

# Web and Search
langchain-community>=0.0.20
wikipedia>=1.4.0
beautifulsoup4>=4.12.0
selenium>=4.15.0

# Communication
email-validator>=2.0.0

# Data Processing
openpyxl>=3.1.0
xlsxwriter>=3.1.0
pillow>=10.0.0
reportlab>=4.0.0

# Audio/Video Processing
librosa>=0.10.0
soundfile>=0.12.0
av>=10.0.0

# Development and Testing
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Optional: Firebase (if needed)
firebase-admin>=6.2.0
google-cloud-storage>=2.10.0

# Optional: Additional ML
huggingface-hub>=0.16.0
tokenizers>=0.13.0
safetensors>=0.3.0
