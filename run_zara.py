#!/usr/bin/env python3
"""
ZARA Voice Assistant 2.0 - Main Launcher
Created by: <PERSON><PERSON>

This script launches the ZARA voice assistant with proper error handling
and environment validation.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def safe_print(text):
    """Print text safely, handling Unicode encoding issues on Windows"""
    try:
        print(text)
    except UnicodeEncodeError:
        # Remove Unicode characters and print ASCII version
        ascii_text = text.encode('ascii', 'ignore').decode('ascii')
        print(ascii_text)

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('zara.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_environment():
    """Check if all required environment variables are set"""
    required_vars = [
        'LIVEKIT_URL',
        'LIVEKIT_API_KEY', 
        'LIVEKIT_API_SECRET'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        safe_print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        safe_print("\n💡 Please check your .env file")
        return False
    
    return True

def check_dependencies():
    """Check if critical dependencies are available"""
    try:
        import livekit
        import livekit.agents
        from dotenv import load_dotenv
        safe_print("✅ Core dependencies found")
        return True
    except ImportError as e:
        safe_print(f"❌ Missing dependency: {e}")
        safe_print("💡 Run: pip install -r requirements.txt")
        return False

def main():
    """Main launcher function"""
    try:
        print("� ZARAe Voice Assistant 2.0")
        print("👨‍💻 Created by: Ratnam Sanjay")
    except UnicodeEncodeError:
        print("ZARA Voice Assistant 2.0")
        print("Created by: Ratnam Sanjay")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    try:
        # Import and run ZARA
        safe_print("🔄 Starting ZARA Voice Assistant...")
        
        from livekit import agents
        from Zara_Voice_Assistant import entrypoint
        
        # Create worker options
        worker_options = agents.WorkerOptions(
            entrypoint_fnc=entrypoint,
            ws_url=os.getenv('LIVEKIT_URL'),
            api_key=os.getenv('LIVEKIT_API_KEY'),
            api_secret=os.getenv('LIVEKIT_API_SECRET')
        )
        
        safe_print("✅ ZARA is ready!")
        safe_print("🎤 Listening for voice commands...")
        
        # Run the agent
        agents.cli.run_app(worker_options)
        
    except KeyboardInterrupt:
        print("\n👋 ZARA shutting down gracefully...")
        logger.info("ZARA shutdown by user")
    except Exception as e:
        safe_print(f"❌ ZARA startup failed: {e}")
        logger.error(f"ZARA startup error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
