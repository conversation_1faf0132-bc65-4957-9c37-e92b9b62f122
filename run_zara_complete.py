#!/usr/bin/env python3
"""
ZARA Complete System Launcher
Created by: <PERSON><PERSON>

This script runs both the ZARA Voice Assistant and GUI in the correct way,
with the voice assistant as a separate process and GUI monitoring it.
"""

import sys
import os
import time
import logging
import argparse
import signal
import subprocess
import threading
from pathlib import Path
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def safe_print(text):
    """Print text safely, handling Unicode encoding issues on Windows"""
    try:
        print(text)
    except UnicodeEncodeError:
        # Remove Unicode characters and print ASCII version
        ascii_text = text.encode('ascii', 'ignore').decode('ascii')
        print(ascii_text)

def setup_logging():
    """Setup comprehensive logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('zara_complete.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_prerequisites():
    """Check all prerequisites"""
    safe_print("🔍 Checking prerequisites...")
    
    # Check dependencies
    missing_deps = []
    try:
        import PyQt6
        import livekit
        from dotenv import load_dotenv
    except ImportError as e:
        missing_deps.append(str(e))
    
    if missing_deps:
        safe_print("❌ Missing dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        return False
    
    # Check files
    required_files = [
        'Zara_Voice_Assistant.py',
        'run_zara.py',
        'zara_gui.py',
        'zara_real_integration.py',
        'zara_status_writer.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        safe_print("❌ Missing files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    # Check environment
    from dotenv import load_dotenv
    load_dotenv()
    
    env_vars = ['LIVEKIT_URL', 'LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET']
    missing_env = []
    for var in env_vars:
        if not os.getenv(var):
            missing_env.append(var)
    
    if missing_env:
        safe_print("⚠️ Missing environment variables (voice assistant may not work):")
        for var in missing_env:
            print(f"   - {var}")
    
    safe_print("✅ Prerequisites check completed")
    return True

def start_voice_assistant():
    """Start the voice assistant as a separate process"""
    safe_print("🎤 Starting ZARA Voice Assistant...")
    
    try:
        # Start voice assistant process with correct arguments
        process = subprocess.Popen(
            [sys.executable, 'run_zara.py', 'start'],
            cwd=str(current_dir),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        safe_print(f"✅ Voice Assistant started with PID: {process.pid}")
        return process
        
    except Exception as e:
        safe_print(f"❌ Failed to start Voice Assistant: {e}")
        return None

def create_gui_with_integration(args):
    """Create GUI with real integration"""
    print("🖥️ Creating ZARA GUI with integration...")
    
    try:
        from zara_gui import ZaraGUI, AppConfig
        from zara_real_integration import RealZaraIntegration
        
        # Create configuration
        config = AppConfig(
            fullscreen=args.fullscreen,
            debug_mode=args.debug,
            log_level=args.log_level,
            performance_monitoring=not args.no_performance,
            window_width=args.width,
            window_height=args.height,
            quality_level=args.quality,
            test_mode=args.test
        )
        
        # Create GUI
        window = ZaraGUI(config=config)
        
        # Create and setup integration
        integration = RealZaraIntegration()
        integration.status_changed.connect(window.status_manager.update_status)
        integration.response_received.connect(window.status_manager.display_response)
        integration.connection_changed.connect(window.status_manager.set_connection_status)
        integration.error_occurred.connect(window.status_manager.set_error_message)
        
        if hasattr(integration, 'voice_activity_detected'):
            integration.voice_activity_detected.connect(window.animation_engine.set_voice_activity)
        
        # Store integration and setup signals
        window.zara_integration = integration
        window.setup_integration_signals()
        
        # Start integration (this will monitor the voice assistant process)
        if integration.start_integration():
            print("✅ Integration started successfully")
        else:
            print("⚠️ Integration startup had issues")
        
        return window, integration
        
    except Exception as e:
        print(f"❌ Failed to create GUI: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def setup_signal_handlers(app, window, voice_process, integration):
    """Setup signal handlers for graceful shutdown"""
    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        
        # Stop integration
        if integration:
            print("🔄 Stopping integration...")
            integration.stop_integration()
        
        # Terminate voice assistant process
        if voice_process and voice_process.poll() is None:
            print("🔄 Stopping voice assistant...")
            voice_process.terminate()
            try:
                voice_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("⚠️ Force killing voice assistant...")
                voice_process.kill()
        
        # Close application
        app.quit()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def monitor_voice_assistant(process, integration):
    """Monitor voice assistant process in background thread"""
    def monitor():
        while True:
            if process.poll() is not None:
                # Process ended
                print(f"⚠️ Voice assistant process ended with code: {process.poll()}")
                break
            time.sleep(5)
    
    monitor_thread = threading.Thread(target=monitor, daemon=True)
    monitor_thread.start()

def main():
    """Main function"""
    safe_print("🚀 ZARA Complete System Launcher")
    safe_print("👨‍💻 Created by: Ratnam Sanjay")
    safe_print("🎤 + 🖥️ = Perfect Integration")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # Parse arguments
    parser = argparse.ArgumentParser(description='Run complete ZARA system with GUI')
    parser.add_argument('--fullscreen', action='store_true', help='Start GUI in fullscreen')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='Set logging level')
    parser.add_argument('--no-performance', action='store_true', help='Disable performance monitoring')
    parser.add_argument('--width', type=int, default=1920, help='Window width')
    parser.add_argument('--height', type=int, default=1080, help='Window height')
    parser.add_argument('--quality', type=int, choices=[0, 1, 2], default=2, help='Animation quality')
    parser.add_argument('--test', action='store_true', help='Enable test mode')
    
    args = parser.parse_args()
    
    try:
        # Check prerequisites
        if not check_prerequisites():
            return 1
        
        # Create Qt application
        print("🔄 Initializing Qt application...")
        app = QApplication(sys.argv)
        app.setApplicationName("ZARA Complete System")
        app.setApplicationVersion("2.0")
        
        # Start voice assistant process
        voice_process = start_voice_assistant()
        if not voice_process:
            print("❌ Cannot continue without voice assistant")
            return 1
        
        # Wait a moment for voice assistant to initialize
        print("⏳ Waiting for voice assistant to initialize...")
        time.sleep(3)
        
        # Create GUI with integration
        window, integration = create_gui_with_integration(args)
        if not window:
            print("❌ Cannot continue without GUI")
            if voice_process:
                voice_process.terminate()
            return 1
        
        # Setup signal handlers
        setup_signal_handlers(app, window, voice_process, integration)
        
        # Start monitoring voice assistant
        monitor_voice_assistant(voice_process, integration)
        
        # Show GUI
        print("🖥️ Showing GUI...")
        window.show()
        
        # Log startup success
        logger.info("ZARA Complete System started successfully")
        logger.info(f"Voice Assistant PID: {voice_process.pid}")
        logger.info(f"Configuration: {window.config}")
        
        print("🎉 ZARA Complete System is now running!")
        print(f"🎤 Voice Assistant: PID {voice_process.pid}")
        print("🖥️ GUI: Interactive and responsive")
        print("🔗 Integration: Monitoring voice assistant")
        print("\n💡 Press Ctrl+C to exit gracefully")
        
        # Start Qt event loop
        exit_code = app.exec()
        
        # Cleanup
        print("\n👋 ZARA Complete System shutting down...")
        
        if integration:
            integration.stop_integration()
        
        if voice_process and voice_process.poll() is None:
            print("🔄 Terminating voice assistant...")
            voice_process.terminate()
            try:
                voice_process.wait(timeout=5)
                print("✅ Voice assistant stopped gracefully")
            except subprocess.TimeoutExpired:
                print("⚠️ Force killing voice assistant...")
                voice_process.kill()
        
        logger.info(f"Application exited with code: {exit_code}")
        return exit_code
        
    except KeyboardInterrupt:
        print("\n👋 Shutdown requested by user")
        return 0
        
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        logger.error(f"Fatal error: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())