import logging
from livekit.agents import function_tool
import requests
from langchain_community.tools import DuckDuckGoSearchRun
import subprocess
import ctypes
import pygetwindow as gw
import platform
import time
import os
import webbrowser
from typing import Optional, Literal
from datetime import datetime
import psutil
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import pyautogui
import re
from dotenv import load_dotenv
import json
import wikipedia
from typing import List, Dict
import asyncio
import aiohttp

# Import memory system
try:
    from memory_system import memory_system, store_conversation, get_recent_context, remember_preference, recall_preference, add_reminder, get_todays_reminders
    MEMORY_AVAILABLE = True
    print("✅ Memory system loaded successfully")
except ImportError as e:
    print(f"⚠️ Memory system not available: {e}")
    MEMORY_AVAILABLE = False

assistant_instance = None

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Constants - Updated for ZARA
YOUTUBE_API_KEY = "AIzaSyDyTQNMwsC1351Iad79b_8mqeGpwYUlMV8"
GMAIL_USER = os.getenv("GMAIL_USER", "<EMAIL>")
GMAIL_PASSWORD = os.getenv("GMAIL_PASSWORD", "Sanju@19207013")

# Safety settings
pyautogui.PAUSE = 0.1
pyautogui.FAILSAFE = True

def validate_email(email: str) -> bool:
    """ईमेल एड्रेस को वैलिडेट करें"""
    pattern = r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"
    return re.match(pattern, email) is not None

@function_tool()
async def get_weather(city: str) -> str:
    """

    Fetches current weather conditions for a specified city in Hindi/English.
    
    Args:
        city (str): The city name to get weather for (e.g., "Delhi")
        
    Returns:
        str: Formatted weather string with temperature and wind speed
        
    Behavior:
        1. First tries Open-Meteo geocoding API
        2. Falls back to OpenStreetMap if needed
        3. Returns temperature (°C) and wind speed (km/h)
        
    Example:
        "Delhi का वर्तमान तापमान है 32°C और पवन की गति है 12 km/h।"
    
    """
    try:
        print(f"🌤️ Getting weather for: {city}")
        
        async with aiohttp.ClientSession() as session:
            # Get location coordinates
            async with session.get(
                f"https://geocoding-api.open-meteo.com/v1/search?name={city}",
                timeout=aiohttp.ClientTimeout(total=5)
            ) as response:
                geo_data = await response.json()

            if not geo_data.get("results"):
                async with session.get(
                    f"https://nominatim.openstreetmap.org/search?q={city}&format=json",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    geo_data = await response.json()
                    if not geo_data:
                        return f"क्षमा करें, मैं स्थान नहीं ढूंढ पाया: {city}."

            location = geo_data[0] if isinstance(geo_data, list) else geo_data["results"][0]
            
            weather_url = (
                f"https://api.open-meteo.com/v1/forecast?"
                f"latitude={location.get('lat', location.get('latitude'))}&"
                f"longitude={location.get('lon', location.get('longitude'))}&"
                f"current_weather=true"
            )
            
            async with session.get(weather_url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                weather_data = await response.json()

            if "current_weather" in weather_data:
                current = weather_data["current_weather"]
                location_name = location.get('display_name', location.get('name', city))
                result = (
                    f"{location_name} का वर्तमान तापमान है {current['temperature']}°C "
                    f"और पवन की गति है {current['windspeed']} km/h।"
                )
                print(f"✅ Weather result: {result}")
                return result
            
            return f"मौसम की जानकारी प्राप्त करने में असमर्थ: {city}"
    except Exception as e:
        logger.error(f"मौसम त्रुटि: {e}")
        return "मौसम सेवा अस्थायी रूप से अनुपलब्ध है। कृपया बाद में प्रयास करें।"

@function_tool()
async def system_power_action(action: Literal["shutdown", "restart", "lock"]) -> str:
    """
    Controls system power state across Windows/Linux/MacOS.
    
    Args:
        action: Power action to perform:
            - "shutdown": Powers off system
            - "restart": Reboots system
            - "lock": Locks workstation
            
    Returns:
        str: Action confirmation in Hindi/English
        
    Security:
        - Requires admin privileges for shutdown/restart
    """
    try:
        print(f"🔧 Power action: {action}")
        
        system = platform.system()
        
        if action == "shutdown":
            if system == "Windows":
                await asyncio.create_task(asyncio.to_thread(os.system, "shutdown /s /t 1"))
            elif system == "Linux":
                await asyncio.create_task(asyncio.to_thread(os.system, "shutdown now"))
            elif system == "Darwin":
                await asyncio.create_task(asyncio.to_thread(os.system, "sudo shutdown -h now"))
            return "सिस्टम शटडाउन किया जा रहा है।"
        
        elif action == "restart":
            if system == "Windows":
                await asyncio.create_task(asyncio.to_thread(os.system, "shutdown /r /t 1"))
            elif system == "Linux":
                await asyncio.create_task(asyncio.to_thread(os.system, "reboot"))
            elif system == "Darwin":
                await asyncio.create_task(asyncio.to_thread(os.system, "sudo shutdown -r now"))
            return "सिस्टम रीस्टार्ट किया जा रहा है।"
        
        elif action == "lock":
            if system == "Windows":
                await asyncio.create_task(asyncio.to_thread(ctypes.windll.user32.LockWorkStation))
            elif system == "Linux":
                await asyncio.create_task(asyncio.to_thread(
                    subprocess.run, ["loginctl", "lock-session"], check=True
                ))
            elif system == "Darwin":
                await asyncio.create_task(asyncio.to_thread(
                    subprocess.run, 
                    ["/System/Library/CoreServices/Menu Extras/User.menu/Contents/Resources/CGSession", "-suspend"]
                ))
            return "🔒 स्क्रीन लॉक की गई है।"
            
    except Exception as e:
        logger.error(f"पावर एक्शन विफल: {e}")
        return f"{action} करने में समस्या आई: {str(e)}"

@function_tool()
async def manage_window(action: Literal["close", "minimize", "maximize"]) -> str:
    """
    Manages the currently active application window.
    
    Args:
        action: Window operation:
            - "close": Terminates window
            - "minimize": Minimizes to taskbar
            - "maximize": Expands window
            
    Returns:
        str: Window title with action confirmation
        
    Notes:
        - Uses pygetwindow for cross-platform support

    """
    try:
        print(f"🪟 Window action: {action}")
        
        # Run pygetwindow operations in thread pool
        active_win = await asyncio.create_task(asyncio.to_thread(gw.getActiveWindow))
        if not active_win:
            return "❌ कोई सक्रिय विंडो नहीं मिली।"

        title = active_win.title.strip() or "अज्ञात विंडो"
        
        if action == "close":
            await asyncio.create_task(asyncio.to_thread(active_win.close))
            return f"✅ '{title}' विंडो बंद कर दी गई।"
            
        elif action == "minimize":
            await asyncio.create_task(asyncio.to_thread(active_win.minimize))
            return f"✅ '{title}' विंडो छोटी की गई।"
            
        elif action == "maximize":
            await asyncio.create_task(asyncio.to_thread(active_win.maximize))
            return f"✅ '{title}' विंडो बड़ी की गई।"
            
    except Exception as e:
        logger.error(f"विंडो प्रबंधन विफल: {e}")
        return f"❌ विंडो {action} करने में समस्या आई: {str(e)}"

@function_tool()
async def get_time_info() -> str:
    """
    Provides current datetime information in Hindi.
    
    Returns:
        str: Formatted string containing:
            - Date (DD-MM-YYYY)
            - Time (12-hour format)
            - Day of week
            
    Example:
        "आज की तारीख है 19-07-2023। अभी का समय है 03:45 PM। सप्ताह का दिन है Wednesday।"
    """
  
    now = datetime.now()
    result = (
        f"आज की तारीख है {now.strftime('%d-%m-%Y')}। "
        f"अभी का समय है {now.strftime('%I:%M %p')}। "
        f"सप्ताह का दिन है {now.strftime('%A')}।"
    )
    
    return result

@function_tool()
async def search_web(query: str) -> str:
    """
    Performs multi-source web search with fallback logic.
    
    Args:
        query: Search terms
        
    Workflow:
        1. Attempts Wikipedia summary
        2. Tries DuckDuckGo API
        3. Falls back to DuckDuckGo search
        
    Returns:
        str: First 2 sentences from Wikipedia or top search result
        
    Notes:
        - Results limited to 500 characters
    """
    try:
        print(f"🔍 Searching web for: {query}")
        
        # Try Wikipedia first
        try:
            summary = await asyncio.create_task(asyncio.to_thread(wikipedia.summary, query, sentences=2))
            print(f"✅ Wikipedia result found")
            return f"📚 विकिपीडिया:\n{summary}"
        except Exception as e:
            print(f"⚠️ Wikipedia failed: {e}")

        # Try DuckDuckGo API
        try:
            async with aiohttp.ClientSession() as session:
                url = "https://api.duckduckgo.com/"
                params = {"q": query, "format": "json", "no_redirect": "1", "no_html": "1"}
                async with session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    data = await response.json()

                if data.get("AbstractText"):
                    print(f"✅ DuckDuckGo API result found")
                    return f"🦆 DuckDuckGo:\n{data['AbstractText']}"
                elif data.get("RelatedTopics"):
                    print(f"✅ DuckDuckGo related topics found")
                    return f"🔍 संबंधित:\n{data['RelatedTopics'][0]['Text']}"
        except Exception as e:
            print(f"⚠️ DuckDuckGo API failed: {e}")

        # Try DuckDuckGo Search tool
        try:
            search_tool = DuckDuckGoSearchRun()
            results = await asyncio.create_task(asyncio.to_thread(search_tool.run, query))
            if results:
                print(f"✅ DuckDuckGo search tool result found")
                return f"🔎 परिणाम:\n{results}"
        except Exception as e:
            print(f"⚠️ DuckDuckGo search tool failed: {e}")

        return "❌ क्षमा करें, अभी कोई उपयोगी जानकारी नहीं मिली।"
    except Exception as e:
        logger.error(f"खोज त्रुटि: {e}")
        return f"❌ वेब खोज में त्रुटि: {e}"

@function_tool()
async def play_media(media_name: str, media_type: Literal["song", "video"] = "song") -> str:
    """
    Plays media content from YouTube.
    
    Args:
        media_name: Name of song/video
        media_type: Content type (default: "song")
        
    Behavior:
        - Uses YouTube Data API if key available
        - Falls back to browser search
        
    Returns:
        str: Currently playing confirmation or search link

    """
    try:
        print(f"🎵 Playing media: {media_name} (type: {media_type})")
        
        if not YOUTUBE_API_KEY:
            await asyncio.create_task(asyncio.to_thread(webbrowser.open, f"https://www.youtube.com/results?search_query={media_name}"))
            return f"YouTube पर '{media_name}' खोल रहा हूँ..."
            
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"https://www.googleapis.com/youtube/v3/search?part=snippet&q={media_name}&type=video&key={YOUTUBE_API_KEY}",
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                data = await response.json()
        
        if data.get('items'):
            video = data['items'][0]
            await asyncio.create_task(asyncio.to_thread(webbrowser.open, f"https://www.youtube.com/watch?v={video['id']['videoId']}"))
            return f"🎵 अब बज रहा है: {video['snippet']['title']}"
        
        await asyncio.create_task(asyncio.to_thread(webbrowser.open, f"https://www.youtube.com/results?search_query={media_name}"))
        return f"YouTube पर '{media_name}' खोल रहा हूँ..."
    except Exception as e:
        logger.error(f"मीडिया त्रुटि: {e}")
        return f"❌ मीडिया चलाने में समस्या आई: {str(e)}"

@function_tool()
async def desktop_control(action: Literal["show", "scroll"], direction: Optional[Literal["up", "down"]] = None, amount: Optional[int] = 3) -> str:
    """
    Controls desktop UI elements.
    
    Args:
        action: "show" desktop or "scroll"
        direction: Scroll direction (required if action=scroll)
        amount: Scroll units (default: 3)
        
    Returns:
        str: Action confirmation
        
    Notes:
        - Restores mouse position after operation
    """
    try:
        print(f"🖥️ Desktop control: {action}")
        
        original_pos = await asyncio.create_task(asyncio.to_thread(pyautogui.position))
        
        if action == "show":
            try:
                await asyncio.create_task(asyncio.to_thread(pyautogui.hotkey, 'win', 'd'))
                return "🖥️ डेस्कटॉप दिखाया जा रहा है।"
            except Exception:
                try:
                    await asyncio.create_task(asyncio.to_thread(pyautogui.click, button='right'))
                    await asyncio.sleep(0.5)
                    await asyncio.create_task(asyncio.to_thread(pyautogui.press, 'm'))
                    return "🖥️ डेस्कटॉप दिखाया जा रहा है।"
                except Exception as e:
                    return f"❌ डेस्कटॉप दिखाने में विफल: {str(e)}"
                    
        elif action == "scroll":
            if not direction:
                direction = "up"
            if not amount:
                amount = 3
                
            try:
                screen_width, screen_height = await asyncio.create_task(asyncio.to_thread(pyautogui.size))
                await asyncio.create_task(asyncio.to_thread(pyautogui.moveTo, screen_width//2, screen_height//2, duration=0.1))
                
                scroll_amount = amount if direction == "up" else -amount
                await asyncio.create_task(asyncio.to_thread(pyautogui.scroll, scroll_amount))
                return f"✅ सफलतापूर्वक {direction} की ओर {amount} यूनिट स्क्रॉल किया।"
            except Exception as e:
                return f"❌ स्क्रॉल करने में विफल: {str(e)}"
                
    except Exception as e:
        return f"❌ डेस्कटॉप कंट्रोल में त्रुटि: {str(e)}"
    finally:
        try:
            await asyncio.create_task(asyncio.to_thread(pyautogui.moveTo, original_pos.x, original_pos.y, duration=0.1))
        except:
            pass

@function_tool()
async def send_email(to_email: str, subject: str, message: str, cc_email: Optional[str] = None) -> str:
    """
    Sends emails via authenticated Gmail SMTP.
    
    Args:
        to_email: Primary recipient
        subject: Email subject
        message: Body content
        cc_email: CC recipient (optional)
        
    Validation:
        - Strict email format validation
        - Requires GMAIL_USER/GMAIL_PASSWORD in .env
        
    Returns:
        str: Delivery confirmation or error
    """
    try:
        print(f"📧 Sending email to: {to_email}")
        
        if not validate_email(to_email):
            return f"❌ अमान्य प्राप्तकर्ता ईमेल: {to_email}"
            
        if cc_email and not validate_email(cc_email):
            return f"❌ अमान्य CC ईमेल: {cc_email}"
            
        if not GMAIL_USER or not GMAIL_PASSWORD:
            return "❌ ईमेल credentials नहीं मिले। कृपया .env फाइल चेक करें।"
            
        def send_email_sync():
            msg = MIMEMultipart()
            msg['From'] = GMAIL_USER
            msg['To'] = to_email
            msg['Subject'] = subject
            
            if cc_email:
                msg['Cc'] = cc_email
                
            msg.attach(MIMEText(message, 'plain'))
            
            with smtplib.SMTP("smtp.gmail.com", 587) as server:
                server.starttls()
                server.login(GMAIL_USER, GMAIL_PASSWORD)
                recipients = [to_email] + ([cc_email] if cc_email else [])
                server.sendmail(GMAIL_USER, recipients, msg.as_string())
                return recipients
        
        recipients = await asyncio.create_task(asyncio.to_thread(send_email_sync))
        return f"✅ ईमेल सफलतापूर्वक भेजा गया: {', '.join(recipients)}"
    except Exception as e:
        logger.error(f"ईमेल त्रुटि: {e}")
        return f"❌ ईमेल भेजने में त्रुटि: {str(e)}"

@function_tool()
async def list_active_windows() -> str:
    """
    Lists all visible application windows.
    
    Returns:
        str: Formatted list with:
            - Window titles
            - Current state (minimized/maximized/active)
            
    Example:
        "• Chrome (Maximized)\n• Notepad (Active)"
    """
    try:
        print("🪟 Listing active windows")
        
        windows = await asyncio.create_task(asyncio.to_thread(gw.getAllWindows))
        result = []
        
        for window in windows:
            if window and window.title:
                try:
                    is_minimized = await asyncio.create_task(asyncio.to_thread(lambda: window.isMinimized))
                    is_maximized = await asyncio.create_task(asyncio.to_thread(lambda: window.isMaximized))
                    status = "Minimized" if is_minimized else "Maximized" if is_maximized else "Active"
                    result.append(f"• {window.title.strip()} ({status})")
                except Exception:
                    continue
        
        if result:
            return f"📋 खुली हुई विंडोज:\n" + "\n".join(result)
        else:
            return "❌ कोई विंडो नहीं मिली"
            
    except Exception as e:
        logger.error(f"विंडो सूची त्रुटि: {e}")
        return f"❌ विंडो डिटेक्शन विफल: {str(e)}"

@function_tool()
async def manage_window_state(action: Literal["maximize", "minimize", "restore"], window_title: Optional[str] = None) -> str:
    """विशिष्ट या सक्रिय विंडो की स्थिति प्रबंधित करें (बड़ा करें, छोटा करें, पुनर्स्थापित करें)"""
    try:
        print(f"🪟 Managing window state: {action} for {window_title or 'active window'}")
        
        if window_title:
            # Find specific window
            all_windows = await asyncio.create_task(asyncio.to_thread(gw.getAllWindows))
            candidates = []
            for win in all_windows:
                if win and win.title and window_title.lower() in win.title.lower():
                    candidates.append(win)
            
            if not candidates:
                return f"❌ '{window_title}' नाम की कोई विंडो नहीं मिली"
            
            target_window = candidates[0]  # Use first match
        else:
            # Use active window
            target_window = await asyncio.create_task(asyncio.to_thread(gw.getActiveWindow))
            if not target_window:
                return "❌ कोई सक्रिय विंडो नहीं मिली"

        try:
            if action == "maximize":
                await asyncio.create_task(asyncio.to_thread(target_window.maximize))
            elif action == "minimize":
                await asyncio.create_task(asyncio.to_thread(target_window.minimize))
            elif action == "restore":
                await asyncio.create_task(asyncio.to_thread(target_window.restore))
                
            return f"✅ विंडो '{target_window.title}' को {action} किया गया"
        except Exception as e:
            return f"❌ विंडो {action} करने में विफल: {str(e)}"
            
    except Exception as e:
        return f"❌ त्रुटि: {str(e)}"

@function_tool()
async def say_reminder(msg: str) -> str:
    """
    Creates audible/visual reminders.
    
    Args:
        msg: Reminder content
        
    Returns:
        str: Formatted reminder with bell icon
        
    Example:
        "🔔 याद दिलाना: Meeting at 3 PM"
    """
    print(f"🔔 Reminder: {msg}")
    return f"🔔 याद दिलाना: {msg}"

# Database and reminder functions
from datetime import datetime, date
import sqlite3

DB_PATH = "zara_memory/zara_memory.db"
TABLE_NAME = "reminders"

async def get_today_reminder_message_from_db() -> str | None:
    """Get today's reminders from the new memory system database"""
    try:
        print(f"🔍 Checking reminders for {datetime.now().strftime('%Y-%m-%d')}")

        if not MEMORY_AVAILABLE:
            print("⚠️ Memory system not available")
            return None

        # Use the new memory system to get today's reminders
        reminders = await get_todays_reminders()

        if not reminders:
            return None

        reminder_messages = []
        for reminder in reminders:
            time_text = f" {reminder['reminder_time']}" if reminder['reminder_time'] else ""
            reminder_messages.append(f"📝 {reminder['title']}{time_text}")
            if reminder['description']:
                reminder_messages.append(f"   📄 {reminder['description']}")

        combined = "\n".join(reminder_messages)
        return f"🧠 सर, आज आपको याद है न — \n{combined}"

    except Exception as e:
        print(f"❌ Error while checking reminders: {e}")
        return None

def extract_date_from_text(text: str) -> Optional[date]:
    """Extract date from text"""
    today = datetime.now().date()

    date_match = re.search(r"\d{4}-\d{2}-\d{2}", text)
    if date_match:
        try:
            return datetime.strptime(date_match.group(), "%Y-%m-%d").date()
        except:
            return None

    if "आज" in text:
        return today
    elif "कल" in text:
        from datetime import timedelta
        return today + timedelta(days=1)

    return None

import asyncio
import os
import subprocess
import pyautogui
import time
from typing import Tuple



@function_tool()
async def send_whatsapp_message(contact: str, message: str) -> str:
    
    """
    Sends WhatsApp messages via desktop automation.
    
    Args:
        contact: Name/number from contacts
        message: Content to send
        
    Workflow:
        1. Opens WhatsApp
        2. Searches contact
        3. Sends message
        
    Returns:
        str: Delivery confirmation and follow-up prompt
    """
    import pyautogui
    import asyncio
    import os

    try:
        print(f"📨 WhatsApp भेजने की प्रक्रिया शुरू: {contact} -> {message}")
        original_pos = await asyncio.to_thread(pyautogui.position)

        # Step 1: Press Win key
        await asyncio.to_thread(pyautogui.press, 'win')
        await asyncio.sleep(1)

        # Step 2: Type "whatsapp" and press Enter
        await asyncio.to_thread(pyautogui.typewrite, 'whatsapp', interval=0.1)
        await asyncio.sleep(1)
        await asyncio.to_thread(pyautogui.press, 'enter')
        await asyncio.sleep(3)

        # Step 3: Ctrl + F to search
        await asyncio.to_thread(pyautogui.hotkey, 'ctrl', 'f')
        await asyncio.sleep(2)

        # Step 4: Type contact name and open chat
        await asyncio.to_thread(pyautogui.typewrite, contact, interval=0.1)
        await asyncio.sleep(1.5)
        await asyncio.to_thread(pyautogui.press, 'down')
        await asyncio.sleep(0.5)
        await asyncio.to_thread(pyautogui.press, 'enter')
        await asyncio.sleep(1.5)

        # Step 5: Send initial message
        await asyncio.to_thread(pyautogui.typewrite, message, interval=0.06)
        await asyncio.to_thread(pyautogui.press, 'enter')
        await asyncio.sleep(1)

        # Step 6: Ask for more (Agent-level trigger only)
        return (
            f"✅ '{contact}' को संदेश भेजा गया: \"{message}\"\n"
            f"🧠 क्या कुछ और भेजना है sir? जवाब दें — ZARA उस संदेश को भेज देगा।"
        )

    except Exception as e:
        return f"❌ संदेश भेजने में त्रुटि: {str(e)}"

    finally:
        try:
            await asyncio.to_thread(pyautogui.moveTo, original_pos.x, original_pos.y, duration=0.1)
        except:
            pass


# PyAutoGUI सेटिंग्स - import करते समय automatically set हो जाएंगी
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.1


@function_tool()
async def write_in_notepad(title: str, content: str, document_type: str = "letter") -> str:
    """
    Creates formatted documents in Notepad.
    
    Args:
        title: Document heading
        content: Main text
        document_type: Format template:
            - "letter": Formal layout
            - "application": Structured
            - "note": Simple text
            
    Returns:
        str: Saved file path confirmation
    """
    import pyautogui
    import asyncio
    import datetime

    try:
        print(f"📝 Starting Notepad writing process: {document_type} - {title}")
        original_pos = await asyncio.to_thread(pyautogui.position)

        # Step 1: Open Notepad using Win key
        print("🔧 Opening Notepad...")
        await asyncio.to_thread(pyautogui.press, 'win')
        await asyncio.sleep(1)

        # Step 2: Type "notepad" and press Enter
        await asyncio.to_thread(pyautogui.typewrite, 'notepad', interval=0.1)
        await asyncio.sleep(1)
        await asyncio.to_thread(pyautogui.press, 'enter')
        await asyncio.sleep(3)  # Wait for Notepad to open

        # Step 3: Create new file (Ctrl+N) to ensure clean slate
        print("📄 Creating new file...")
        await asyncio.to_thread(pyautogui.hotkey, 'ctrl', 'n')
        await asyncio.sleep(1)

        # Step 4: Clear any existing content (Ctrl+A, Delete)
        await asyncio.to_thread(pyautogui.hotkey, 'ctrl', 'a')
        await asyncio.sleep(0.5)
        await asyncio.to_thread(pyautogui.press, 'delete')
        await asyncio.sleep(0.5)

        # Step 5: Start writing the document with proper formatting
        print("✍️ Writing document content...")
        
        # Add date at the top
        current_date = datetime.datetime.now().strftime("%d/%m/%Y")
        await asyncio.to_thread(pyautogui.typewrite, f"Date: {current_date}", interval=0.05)
        await asyncio.to_thread(pyautogui.press, 'enter')
        await asyncio.to_thread(pyautogui.press, 'enter')

        # Add document title
        await asyncio.to_thread(pyautogui.typewrite, f"Subject: {title}", interval=0.05)
        await asyncio.to_thread(pyautogui.press, 'enter')
        await asyncio.to_thread(pyautogui.press, 'enter')

        # Add greeting for letters/applications
        if document_type.lower() in ["letter", "application"]:
            await asyncio.to_thread(pyautogui.typewrite, "Dear Sir/Madam,", interval=0.05)
            await asyncio.to_thread(pyautogui.press, 'enter')
            await asyncio.to_thread(pyautogui.press, 'enter')

        # Write main content with proper paragraph formatting
        paragraphs = content.split('\n\n')
        for i, paragraph in enumerate(paragraphs):
            if paragraph.strip():  # Skip empty paragraphs
                # Clean the paragraph text
                clean_paragraph = paragraph.strip()
                await asyncio.to_thread(pyautogui.typewrite, clean_paragraph, interval=0.03)
                await asyncio.to_thread(pyautogui.press, 'enter')
                await asyncio.to_thread(pyautogui.press, 'enter')

        # Add professional closing for letters/applications
        if document_type.lower() in ["letter", "application"]:
            await asyncio.to_thread(pyautogui.typewrite, "Thank you for your time and consideration.", interval=0.05)
            await asyncio.to_thread(pyautogui.press, 'enter')
            await asyncio.to_thread(pyautogui.press, 'enter')
            await asyncio.to_thread(pyautogui.typewrite, "Yours sincerely,", interval=0.05)
            await asyncio.to_thread(pyautogui.press, 'enter')
            await asyncio.to_thread(pyautogui.press, 'enter')
            await asyncio.to_thread(pyautogui.typewrite, "[Your Name]", interval=0.05)

        # Step 6: Save the document
        print("💾 Saving document...")
        await asyncio.sleep(1)
        await asyncio.to_thread(pyautogui.hotkey, 'ctrl', 's')
        await asyncio.sleep(2)
        
        # Create clean filename
        safe_title = title.replace(' ', '_').replace('/', '_').replace('\\', '_')
        filename = f"{document_type}_{safe_title}_{current_date.replace('/', '_')}.txt"
        
        await asyncio.to_thread(pyautogui.typewrite, filename, interval=0.05)
        await asyncio.to_thread(pyautogui.press, 'enter')
        await asyncio.sleep(1)

        print("✅ Document created successfully!")
        return (
            f"✅ '{title}' {document_type} successfully created in Notepad\n"
            f"📄 File saved as: {filename}\n"
            f"🎯 Document type: {document_type.title()}\n"
            f"📝 Content written with proper formatting\n"
            f"🔄 New file created for clean writing experience"
        )

    except Exception as e:
        error_msg = f"❌ Error writing to Notepad: {str(e)}"
        print(error_msg)
        return error_msg

    finally:
        try:
            # Return mouse to original position
            await asyncio.to_thread(pyautogui.moveTo, original_pos.x, original_pos.y, duration=0.1)
        except:
            pass


# PyAutoGUI Configuration
import pyautogui
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.1



@function_tool()
async def open_app(app_name: str) -> str:
    """
    Launches applications via Start Menu search.
    
    Args:
        app_name: Application name (e.g., "chrome")
        
    Returns:
        str: Launch confirmation or error
        
    Notes:
        - Windows-specific implementation
    """
    import pyautogui
    import asyncio

    try:
        print(f"🚀 ऐप खोलने का प्रयास: {app_name}")
        original_pos = await asyncio.to_thread(pyautogui.position)

        # Step 1: Press Win key to open start menu
        await asyncio.to_thread(pyautogui.press, 'win')
        await asyncio.sleep(1)

        # Step 2: Type app name
        await asyncio.to_thread(pyautogui.typewrite, app_name, interval=0.1)
        await asyncio.sleep(1)

        # Step 3: Press Enter to open the app
        await asyncio.to_thread(pyautogui.press, 'enter')

        return f"✅ '{app_name}' खोल दिया गया है।"

    except Exception as e:
        return f"❌ ऐप खोलने में त्रुटि: {str(e)}"

    finally:
        try:
            await asyncio.to_thread(pyautogui.moveTo, original_pos.x, original_pos.y, duration=0.1)
        except:
            pass


@function_tool()
async def press_key(key: str) -> str:
    """
    Simulates keyboard key presses.
    
    Args:
        key: Single key ("enter") or combo ("ctrl+alt+del")
        
    Returns:
        str: Press confirmation
        
    Notes:
        - Supports most standard keyboard keys
    """
    import pyautogui
    import asyncio

    try:
        # Normalize input
        key = key.strip().lower()

        # Split combination if needed
        if '+' in key:
            keys = [k.strip() for k in key.split('+')]
            await asyncio.to_thread(pyautogui.hotkey, *keys)
        else:
            await asyncio.to_thread(pyautogui.press, key)

        return f"✅ '{key}' दबा दिया गया है।"

    except Exception as e:
        return f"❌ कुंजी दबाने में त्रुटि: {str(e)}"
    


@function_tool()
async def get_system_info() -> str:
    """
    Provides comprehensive system diagnostics.
    
    Returns:
        str: Formatted report containing:
            - Battery status
            - Storage space
            - Network info
            - CPU/RAM usage
            
    Metrics:
        - Updates in real-time
    """
    import psutil
    import socket
    import platform
    import shutil

    try:
        # Battery Info
        battery = psutil.sensors_battery()
        if battery:
            battery_percent = battery.percent
            charging = "⚡ Charging" if battery.power_plugged else "🔋 On Battery"
        else:
            battery_percent = "N/A"
            charging = "N/A"

        # Storage Info
        total, used, free = shutil.disk_usage("/")
        total_gb = total // (2**30)
        free_gb = free // (2**30)

        # Network Info
        try:
            hostname = socket.gethostname()
            ip_address = socket.gethostbyname(hostname)
            network_status = f"Connected (IP: {ip_address})"
        except:
            network_status = "❌ Not Connected"

        # CPU & RAM
        cpu_percent = psutil.cpu_percent(interval=1)
        ram = psutil.virtual_memory()
        ram_percent = ram.percent
        ram_total_gb = round(ram.total / (1024 ** 3), 1)
        ram_used_gb = round(ram.used / (1024 ** 3), 1)

        # System Name
        system_name = platform.node()

        return (
            f"🧠 System Info for: {system_name}\n"
            f"🔋 Battery: {battery_percent}% ({charging})\n"
            f"💾 Storage: {free_gb} GB free of {total_gb} GB\n"
            f"📶 Network: {network_status}\n"
            f"🧠 CPU Usage: {cpu_percent}%\n"
            f"📈 RAM Usage: {ram_percent}% ({ram_used_gb} GB of {ram_total_gb} GB)"
        )

    except Exception as e:
        return f"❌ सिस्टम जानकारी प्राप्त करने में त्रुटि: {str(e)}"



@function_tool()
async def type_user_message_auto(message: str) -> str:
    """
    Types content into active window.
    
    Args:
        message: Text to type
        
    Returns:
        str: Typing confirmation
        
    Behavior:
        - Natural typing speed (0.1s intervals)
        - Preserves original cursor position

    """
    import pyautogui
    import asyncio

    if not message.strip():
        return "⚠️ Sir, message खाली है।"

    await asyncio.to_thread(pyautogui.typewrite, message, interval=0.1)
    return f"✅ टाइप कर दिया गया: \"{message}\""


import numpy as np
import cv2
from mss import mss
import win32gui
import time
from PIL import Image
import os
from datetime import datetime
import re
from functools import wraps
import json

# Configure Tesseract OCR with multiple possible paths
def configure_tesseract():
    """Configure Tesseract OCR with automatic path detection"""
    try:
        import pytesseract

        # Try common installation paths
        possible_paths = [
            r'C:\Program Files\Tesseract-OCR\tesseract.exe',
            r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
            r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', '')),
            'tesseract',  # If in PATH
        ]

        for path in possible_paths:
            try:
                if path == 'tesseract':
                    # Test if tesseract is in PATH
                    pytesseract.pytesseract.tesseract_cmd = 'tesseract'
                    pytesseract.get_tesseract_version()
                    print(f"✅ Tesseract found in PATH")
                    return True
                elif os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    pytesseract.get_tesseract_version()
                    print(f"✅ Tesseract found at: {path}")
                    return True
            except:
                continue

        print("⚠️ Tesseract OCR not found. Text recognition will be limited.")
        return False

    except ImportError:
        print("⚠️ pytesseract not installed. Install with: pip install pytesseract")
        return False

# Configure Tesseract on import
TESSERACT_AVAILABLE = configure_tesseract()
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

# Global setup
sct = mss()
monitor = sct.monitors[1]



@function_tool()
async def click_on_text(target_text: str) -> str:
    """
    Clicks on screen text using OCR.
    
    Args:
        target_text: Visible text to click
        
    Returns:
        str: Click confirmation or error
        
    Technology:
        - Uses Tesseract OCR
        - Fuzzy text matching
    """
    import pyautogui
    import pytesseract
    import cv2
    import numpy as np
    import asyncio
    from difflib import SequenceMatcher

    def similarity(text1: str, text2: str) -> float:
        return SequenceMatcher(None, text1.lower().strip(), text2.lower().strip()).ratio()

    try:
        # Check if Tesseract is available
        if not TESSERACT_AVAILABLE:
            return "❌ Tesseract OCR not available. Run 'python install_tesseract.py' to install."

        try:
            import pytesseract
            pytesseract.get_tesseract_version()
        except Exception as e:
            return f"❌ Tesseract error: {str(e)}"

        # Screenshot
        screenshot = await asyncio.to_thread(pyautogui.screenshot)
        screenshot_np = np.array(screenshot)
        image = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

        # Preprocess
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray = cv2.resize(gray, None, fx=2, fy=2, interpolation=cv2.INTER_CUBIC)

        # OCR
        data = await asyncio.to_thread(
            pytesseract.image_to_data,
            gray,
            output_type=pytesseract.Output.DICT
        )
        
        # Find best match
        best_match = None
        best_score = 0
        
        for i in range(len(data['text'])):
            text = data['text'][i].strip()
            if not text:
                continue
            
            score = similarity(target_text, text)
            if score > best_score and score > 0.7:
                best_score = score
                best_match = {
                    'text': text,
                    'x': int(data['left'][i] / 2),
                    'y': int(data['top'][i] / 2),
                    'w': int(data['width'][i] / 2),
                    'h': int(data['height'][i] / 2)
                }
        
        if not best_match:
            return f"❌ '{target_text}' नहीं मिला"
        
        # Click
        center_x = best_match['x'] + best_match['w'] // 2
        center_y = best_match['y'] + best_match['h'] // 2
        
        await asyncio.to_thread(pyautogui.moveTo, center_x, center_y, duration=0.2)
        await asyncio.to_thread(pyautogui.click)
        
        return f"✅ '{target_text}' पर क्लिक किया गया!"

    except Exception as e:
        return f"🚫 Error: {str(e)}"
    

@function_tool()
async def scan_system_for_viruses() -> str:
    """
    Performs quick virus scan using Windows Defender.
    
    Returns:
        str: Scan summary with:
            - Threats found
            - Scan duration
            - Last update
            
    Notes:
        - Requires admin privileges
    """
    import asyncio
    import subprocess

    try:
        cmd = [
            r"C:\Program Files\Windows Defender\MpCmdRun.exe",  # Older path
            "-Scan", "-ScanType", "1"  # 1 = quick scan
        ]

        # fallback path for Windows 10+
        alt_cmd = [
            r"C:\ProgramData\Microsoft\Windows Defender\Platform\4.18.23070.2003-0\MpCmdRun.exe",
            "-Scan", "-ScanType", "1"
        ]

        try:
            proc = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
        except FileNotFoundError:
            proc = await asyncio.create_subprocess_exec(
                *alt_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

        stdout, stderr = await proc.communicate()

        output = stdout.decode().strip()
        if "Scan starting" in output or "Scan completed" in output:
            return f"🛡️ सिस्टम स्कैन पूरा हुआ:\n\n{output[-500:]}"
        else:
            return f"⚠️ स्कैन पूरा हुआ, लेकिन कोई जानकारी नहीं मिली:\n\n{output[-500:]}"
        
    except Exception as e:
        return f"❌ स्कैन में त्रुटि: {str(e)}"
    



@function_tool()
async def enable_camera_analysis(enable: bool) -> str:
    """
    Enable or disable camera feed analysis. When enabled, the assistant will process video frames.
    
    Args:
        enable: True to enable camera analysis, False to disable
        assistant_instance: The assistant instance to control
        
    Returns:
        str: Confirmation message
    """
    try:
        if assistant_instance is None:
           return "Assistant instance not set"
        return await assistant_instance.enable_visual_analysis(enable)
    except Exception as e:
        return f"Failed to {'enable' if enable else 'disable'} camera analysis: {str(e)}"
    


@function_tool()
async def analyze_visual_scene(prompt: str) -> str:
    """
    Analyzes camera feed based on prompt.
    
    Args:
        prompt: Analysis request (e.g., "count objects")
        
    Returns:
        str: Analysis results
        
    Workflow:
        1. Temporarily enables camera
        2. Processes frames
        3. Returns findings
    """
    try:
        # Enable camera temporarily
        await assistant_instance.enable_visual_analysis(True)
        await asyncio.sleep(0.5)
        result = await assistant_instance.analyze_current_scene(prompt)
        await assistant_instance.enable_visual_analysis(False)
        return result
        
    except Exception as e:
        return f"Visual analysis failed: {str(e)}"
    

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
from typing import Dict, List, Any, Optional
import json
from pathlib import Path
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import tkinter as tk
from tkinter import filedialog
import os
import webbrowser
import asyncio
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import io
import base64
import multiprocessing as mp
from functools import partial

warnings.filterwarnings('ignore')

# Optimize pandas settings for speed
pd.set_option('compute.use_bottleneck', True)
pd.set_option('compute.use_numexpr', True)

# Global DataFrame storage
GLOBAL_DF = None
ANALYSIS_CACHE = {
    'data': None,
    'insights': {},
    'metadata': {},
    'analysis_results': {},
    'visualizations': {},
    'business_context': {}
}

# Optimized helper functions
@function_tool()
async def _open_file_dialog() -> str:
    """Open file dialog to select Excel/CSV file"""
    try:
        reset_analysis()
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)
        root.focus_force()
        
        file_path = filedialog.askopenfilename(
            title="Select Data File",
            filetypes=[
                ("Excel files", "*.xlsx *.xls"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        
        root.destroy()
        return file_path if file_path else ""
        
    except Exception as e:
        print(f"Error in file dialog: {e}")
        return ""

def _load_data_file(file_path: str) -> pd.DataFrame:
    """Ultra-fast data loading with optimizations"""
    try:
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == '.csv':
            # Fast CSV loading with optimizations
            df = pd.read_csv(
                file_path,
                encoding='utf-8',
                low_memory=False,
                engine='c',  # Use C engine for speed
                skipinitialspace=True,
                na_values=['', 'NULL', 'null', 'NaN', 'nan', 'N/A', 'n/a']
            )
                
        elif file_ext in ['.xlsx', '.xls']:
            # Fast Excel loading
            if file_ext == '.xlsx':
                df = pd.read_excel(file_path, engine='openpyxl')
            else:
                df = pd.read_excel(file_path, engine='xlrd')
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")
        
        # Fast column cleaning
        df.columns = df.columns.astype(str).str.strip()
        
        # Fast data cleaning
        df = df.dropna(how='all').reset_index(drop=True)
        unnamed_cols = df.columns.str.contains('^Unnamed', na=False)
        df = df.loc[:, ~unnamed_cols]
        
        return df
        
    except Exception as e:
        print(f"Error loading file: {e}")
        return None

def _detect_column_types_fast(df: pd.DataFrame) -> Dict[str, str]:
    """Ultra-fast column type detection"""
    type_info = {}
    
    # Vectorized operations for speed
    for col in df.columns:
        col_data = df[col]
        
        if col_data.isna().all():
            type_info[col] = 'empty'
            continue
        
        # Fast type detection using pandas dtype
        dtype = col_data.dtype
        
        if pd.api.types.is_numeric_dtype(dtype):
            type_info[col] = 'numeric'
        elif pd.api.types.is_datetime64_any_dtype(dtype):
            type_info[col] = 'datetime'
        else:
            # Quick sampling for non-numeric columns
            sample_size = min(100, len(col_data.dropna()))
            if sample_size > 0:
                sample = col_data.dropna().iloc[:sample_size]
                sample_str = sample.astype(str).str.lower()
                
                if sample_str.str.contains('date|2020|2021|2022|2023|2024|2025', na=False).any():
                    type_info[col] = 'potential_date'
                elif col.lower().endswith('_id') or 'id' in col.lower():
                    type_info[col] = 'identifier'
                else:
                    unique_ratio = len(sample.unique()) / len(sample)
                    type_info[col] = 'categorical' if unique_ratio < 0.5 else 'text'
            else:
                type_info[col] = 'text'
    
    return type_info

def _detect_business_context_fast(df: pd.DataFrame) -> Dict[str, Any]:
    """Ultra-fast business context detection"""
    context = {
        'domain': 'unknown',
        'key_metrics': [],
        'time_columns': [],
        'id_columns': [],
        'categorical_columns': [],
        'numeric_columns': []
    }
    
    # Vectorized column classification
    columns_lower = pd.Series(df.columns).str.lower()
    
    # Fast domain detection
    domain_keywords = {
        'sales': ['sales', 'revenue', 'profit', 'customer', 'order', 'amount', 'price'],
        'hr': ['employee', 'salary', 'department', 'performance', 'rating'],
        'inventory': ['product', 'stock', 'quantity', 'warehouse', 'inventory'],
        'marketing': ['campaign', 'clicks', 'impressions', 'conversion', 'marketing'],
        'financial': ['expense', 'cost', 'budget', 'account', 'balance', 'financial']
    }
    
    all_text = ' '.join(columns_lower)
    max_score = 0
    for domain, keywords in domain_keywords.items():
        score = sum(1 for keyword in keywords if keyword in all_text)
        if score > max_score:
            max_score = score
            context['domain'] = domain
    
    # Fast column classification using vectorized operations
    time_mask = columns_lower.str.contains('date|time|month|year', na=False)
    id_mask = columns_lower.str.contains('id|_id$', na=False)
    
    context['time_columns'] = df.columns[time_mask].tolist()
    context['id_columns'] = df.columns[id_mask].tolist()
    
    # Fast numeric column detection
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    context['numeric_columns'] = numeric_cols
    
    # Fast key metrics detection
    metric_keywords = ['amount', 'price', 'cost', 'revenue', 'profit', 'sales', 'quantity', 'total', 'value', 'score']
    for col in numeric_cols:
        if any(keyword in col.lower() for keyword in metric_keywords):
            context['key_metrics'].append(col)
    
    # Fast categorical column detection
    for col in df.columns:
        if col not in numeric_cols and col not in context['time_columns'] and col not in context['id_columns']:
            if df[col].nunique() < len(df) * 0.5:
                context['categorical_columns'].append(col)
    
    return context

def _analyze_data_quality_fast(df: pd.DataFrame) -> Dict[str, Any]:
    """Ultra-fast data quality analysis"""
    total_cells = len(df) * len(df.columns)
    missing_count = df.isnull().sum().sum()
    
    quality = {
        'total_rows': len(df),
        'total_columns': len(df.columns),
        'missing_percentage': (missing_count / total_cells) * 100 if total_cells > 0 else 0,
        'duplicate_rows': df.duplicated().sum(),
        'quality_score': 0
    }
    
    # Fast quality score calculation
    quality_score = 100 - quality['missing_percentage'] - (quality['duplicate_rows'] / len(df) * 100)
    quality['quality_score'] = max(0, quality_score)
    
    return quality

def _find_key_insights_fast(df: pd.DataFrame, business_context: Dict) -> Dict[str, Any]:
    """Ultra-fast insight extraction"""
    insights = {
        'top_performers': {},
        'correlations': [],
        'distributions': {},
        'business_insights': []
    }
    
    # Fast metrics calculation for key metrics only
    key_metrics = business_context['key_metrics'][:5]  # Limit to top 5 for speed
    
    for metric in key_metrics:
        if metric in df.columns:
            col_data = df[metric].dropna()
            if len(col_data) > 0:
                insights['top_performers'][metric] = {
                    'max_value': float(col_data.max()),
                    'min_value': float(col_data.min()),
                    'mean': float(col_data.mean()),
                    'median': float(col_data.median()),
                    'std': float(col_data.std())
                }
    
    # Fast correlation calculation (limit to top numeric columns)
    numeric_cols = business_context['numeric_columns'][:8]  # Limit for speed
    if len(numeric_cols) > 1:
        corr_matrix = df[numeric_cols].corr()
        
        # Vectorized correlation extraction
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool), k=1)
        corr_values = corr_matrix.where(mask)
        
        for i in range(len(numeric_cols)):
            for j in range(i+1, len(numeric_cols)):
                corr_value = corr_values.iloc[i, j]
                if not pd.isna(corr_value) and abs(corr_value) > 0.7:
                    insights['correlations'].append({
                        'column1': numeric_cols[i],
                        'column2': numeric_cols[j],
                        'correlation': float(corr_value),
                        'strength': 'strong positive' if corr_value > 0 else 'strong negative'
                    })
    
    # Fast business insights generation
    insights['business_insights'] = _generate_business_insights_fast(df, business_context, insights)
    
    return insights

def _generate_business_insights_fast(df: pd.DataFrame, business_context: Dict, insights: Dict) -> List[str]:
    """Ultra-fast business insight generation"""
    business_insights = []
    
    # Fast performance insights (limit to top 3 metrics)
    top_metrics = list(insights['top_performers'].items())[:3]
    for metric, data in top_metrics:
        if data['max_value'] > 0:
            range_pct = ((data['max_value'] - data['min_value']) / data['mean']) * 100
            business_insights.append(
                f"The {metric} shows high variability with a {range_pct:.1f}% range from mean, "
                f"indicating potential opportunities for optimization."
            )
    
    # Fast correlation insights (limit to top 2)
    for corr in insights['correlations'][:2]:
        business_insights.append(
            f"Strong {corr['strength']} correlation ({corr['correlation']:.3f}) between "
            f"{corr['column1']} and {corr['column2']} suggests potential business relationship."
        )
    
    # Fast data quality insight
    missing_pct = (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
    if missing_pct > 10:
        business_insights.append(f"Data quality requires attention: {missing_pct:.1f}% missing values detected.")
    
    # Domain-specific insights
    domain = business_context['domain']
    domain_insights = {
        'sales': "Sales analysis reveals performance patterns across different metrics.",
        'hr': "HR data analysis shows employee performance and organizational metrics.",
        'inventory': "Inventory analysis indicates stock levels and product performance.",
        'marketing': "Marketing metrics show campaign effectiveness and engagement patterns.",
        'financial': "Financial analysis reveals cost structures and budget allocations."
    }
    
    if domain in domain_insights:
        business_insights.append(domain_insights[domain])
    
    return business_insights

def _create_visualizations_fast(df: pd.DataFrame, business_context: Dict) -> Dict[str, str]:
    """Ultra-fast visualization creation"""
    visualizations = {}
    
    # Use faster matplotlib backend
    plt.switch_backend('Agg')
    plt.style.use('fast')
    
    # 1. Fast key metrics overview (limit to top 4)
    key_metrics = business_context['key_metrics'][:4]
    if key_metrics:
        fig, axes = plt.subplots(2, 2, figsize=(10, 6))
        fig.suptitle('Key Metrics Overview', fontsize=12, fontweight='bold')
        
        for i, metric in enumerate(key_metrics):
            row, col = i // 2, i % 2
            # Fast histogram with reduced bins
            df[metric].hist(bins=15, ax=axes[row, col], alpha=0.7, color='skyblue')
            axes[row, col].set_title(f'{metric} Distribution', fontsize=10)
            axes[row, col].grid(True, alpha=0.3)
        
        # Hide unused subplots
        for i in range(len(key_metrics), 4):
            row, col = i // 2, i % 2
            axes[row, col].set_visible(False)
        
        plt.tight_layout()
        metrics_path = 'key_metrics_overview.png'
        plt.savefig(metrics_path, dpi=150, bbox_inches='tight')  # Reduced DPI for speed
        visualizations['key_metrics'] = metrics_path
        plt.close()
    
    # 2. Fast correlation heatmap (limit columns)
    numeric_cols = business_context['numeric_columns'][:8]  # Limit for speed
    if len(numeric_cols) > 1:
        plt.figure(figsize=(8, 6))
        corr_matrix = df[numeric_cols].corr()
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, square=True, fmt='.2f')
        plt.title('Correlation Matrix', fontsize=12)
        plt.tight_layout()
        
        corr_path = 'correlation_heatmap.png'
        plt.savefig(corr_path, dpi=150, bbox_inches='tight')
        visualizations['correlations'] = corr_path
        plt.close()
    
    return visualizations
def _generate_html_report_fast(df: pd.DataFrame, insights: Dict, quality: Dict, business_context: Dict) -> str:
    """Ultra-fast HTML report generation with enhanced dark theme design"""
    try:
        timestamp = datetime.now().strftime('%B %d, %Y at %I:%M %p')
        
        # Safely get values with defaults
        domain = business_context.get('domain', 'unknown domain').title()
        key_metrics_count = len(business_context.get('key_metrics', []))
        quality_score = quality.get('quality_score', 0)
        missing_percentage = quality.get('missing_percentage', 0)
        duplicate_rows = quality.get('duplicate_rows', 0)
        outliers = quality.get('outliers', 0)
        inconsistencies = quality.get('inconsistencies', 0)
        
        # Safely handle insights
        top_performers = insights.get('top_performers', {})
        business_insights = insights.get('business_insights', [])
        correlations = insights.get('correlations', [])
        
        # Dark theme HTML template with modern design
        html_content = f"""<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Advanced Data Analysis Report</title>
<style>
:root {{
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --secondary: #8b5cf6;
    --accent: #ec4899;
    --success: #22c55e;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #06b6d4;
    --dark: #0f172a;
    --darker: #020617;
    --gray: #94a3b8;
    --light: #f8fafc;
    --border: #1e293b;
    --card-bg: rgba(30, 41, 59, 0.6);
}}
* {{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}}
body {{
    font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
    background: linear-gradient(135deg, var(--darker), var(--dark));
    color: var(--light);
    line-height: 1.6;
    padding: 20px;
    min-height: 100vh;
}}
.report-container {{
    max-width: 1200px;
    margin: 20px auto;
    background: rgba(15, 23, 42, 0.8);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 25px 50px -12px rgba(0,0,0,0.25);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(99, 102, 241, 0.2);
}}
.report-header {{
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    padding: 50px 40px;
    position: relative;
    text-align: center;
    overflow: hidden;
}}
.report-header::before {{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--success), var(--info), var(--warning), var(--accent));
}}
.header-content {{
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}}
.report-title {{
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 15px;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 10px rgba(0,0,0,0.2);
}}
.report-subtitle {{
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 25px;
    font-weight: 400;
}}
.report-meta {{
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 25px;
    flex-wrap: wrap;
}}
.meta-item {{
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    padding: 12px 24px;
    border-radius: 50px;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255,255,255,0.1);
}}
.section {{
    padding: 45px;
    border-bottom: 1px solid var(--border);
}}
.section:last-child {{
    border-bottom: none;
}}
.section-title {{
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 35px;
    display: flex;
    align-items: center;
    gap: 15px;
}}
.section-title::after {{
    content: "";
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), transparent);
    margin-left: 20px;
    opacity: 0.3;
}}
.metric-grid {{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 30px;
    margin: 35px 0;
}}
.metric-card {{
    background: var(--card-bg);
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    border: 1px solid rgba(99, 102, 241, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}}
.metric-card:hover {{
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.25);
    border-color: rgba(99, 102, 241, 0.4);
}}
.metric-card::before {{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
}}
.metric-value {{
    font-size: 2.8rem;
    font-weight: 800;
    margin: 20px 0;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}}
.metric-label {{
    color: var(--gray);
    font-size: 1.1rem;
    font-weight: 500;
}}
.insight-grid {{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    margin-top: 30px;
}}
.insight-card {{
    background: var(--card-bg);
    border-radius: 16px;
    padding: 30px;
    border-left: 4px solid var(--primary);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}}
.insight-card:hover {{
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.25);
}}
.insight-card::before {{
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--primary), transparent 70%);
    border-radius: 0 16px 0 100%;
}}
.insight-content {{
    font-size: 1.15rem;
    line-height: 1.7;
    position: relative;
    z-index: 2;
}}
.insight-icon {{
    font-size: 2rem;
    margin-bottom: 20px;
    color: var(--primary);
    position: relative;
    z-index: 2;
}}
.correlation-item {{
    display: flex;
    align-items: center;
    padding: 25px;
    background: var(--card-bg);
    border-radius: 16px;
    margin: 20px 0;
    border-left: 4px solid var(--info);
    transition: all 0.3s ease;
}}
.correlation-item:hover {{
    transform: translateX(5px);
    border-left-width: 6px;
}}
.stats-table {{
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 35px 0;
    overflow: hidden;
    border-radius: 16px;
    background: var(--card-bg);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}}
.stats-table th {{
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: white;
    padding: 20px 25px;
    text-align: left;
    font-weight: 600;
    font-size: 1.1rem;
}}
.stats-table td {{
    padding: 18px 25px;
    border-bottom: 1px solid var(--border);
    color: var(--light);
}}
.stats-table tr:last-child td {{
    border-bottom: none;
}}
.stats-table tr:nth-child(even) {{
    background-color: rgba(30, 41, 59, 0.4);
}}
.stats-table tr:hover td {{
    background-color: rgba(30, 41, 59, 0.8);
}}
.executive-summary {{
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.7), rgba(15, 23, 42, 0.8));
    border-radius: 20px;
    padding: 40px;
    margin-top: 30px;
    border: 1px solid rgba(99, 102, 241, 0.2);
    position: relative;
    overflow: hidden;
}}
.executive-summary::before {{
    content: "";
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
}}
.summary-points {{
    margin: 25px 0 0 30px;
}}
.summary-points li {{
    margin-bottom: 20px;
    position: relative;
    padding-left: 35px;
    font-size: 1.1rem;
    line-height: 1.8;
}}
.summary-points li::before {{
    content: "•";
    color: var(--primary);
    font-size: 2rem;
    position: absolute;
    left: 0;
    top: -5px;
}}
.report-footer {{
    text-align: center;
    padding: 40px;
    background: rgba(2, 6, 23, 0.7);
    color: rgba(255,255,255,0.7);
    position: relative;
}}
.report-footer::before {{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary), transparent);
}}
.footer-note {{
    margin-top: 15px;
    font-size: 0.95rem;
    opacity: 0.7;
}}
.correlation-strength {{
    display: inline-block;
    padding: 6px 15px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-left: 10px;
}}
.strong {{
    background: rgba(34, 197, 94, 0.15);
    color: var(--success);
    border: 1px solid rgba(34, 197, 94, 0.3);
}}
.moderate {{
    background: rgba(245, 158, 11, 0.15);
    color: var(--warning);
    border: 1px solid rgba(245, 158, 11, 0.3);
}}
.weak {{
    background: rgba(239, 68, 68, 0.15);
    color: var(--danger);
    border: 1px solid rgba(239, 68, 68, 0.3);
}}
.glow {{
    text-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
}}
@keyframes fadeIn {{
    from {{ opacity: 0; transform: translateY(20px); }}
    to {{ opacity: 1; transform: translateY(0); }}
}}
.section, .report-header, .report-footer {{
    animation: fadeIn 0.6s ease forwards;
}}
@media (max-width: 768px) {{
    .section {{
        padding: 35px 25px;
    }}
    .metric-grid {{
        grid-template-columns: 1fr;
    }}
    .report-header {{
        padding: 40px 25px;
    }}
    .report-title {{
        font-size: 2.4rem;
    }}
}}
</style>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
<div class="report-container">
    <div class="report-header">
        <div class="header-content">
            <h1 class="report-title glow">Advanced Data Insights Report</h1>
            <p class="report-subtitle">Dark Theme Edition • Comprehensive Analytics</p>
            
            <div class="report-meta">
                <div class="meta-item">
                    <span>📅</span> {timestamp}
                </div>
                <div class="meta-item">
                    <span>🏢</span> Domain: {domain}
                </div>
                <div class="meta-item">
                    <span>📊</span> {len(df):,} records × {len(df.columns):,} attributes
                </div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2 class="section-title"><span>📈</span> Dataset Overview</h2>
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-value">{len(df):,}</div>
                <div class="metric-label">Total Records</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{len(df.columns):,}</div>
                <div class="metric-label">Data Attributes</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{quality_score:.1f}%</div>
                <div class="metric-label">Quality Score</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{key_metrics_count}</div>
                <div class="metric-label">Key Metrics</div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2 class="section-title"><span>📊</span> Key Performance Insights</h2>"""
        
        # Safe table generation
        if top_performers:
            html_content += '<table class="stats-table"><tr><th>Metric</th><th>Maximum</th><th>Minimum</th><th>Average</th><th>Median</th></tr>'
            for metric, data in list(top_performers.items())[:5]:
                max_val = data.get("max_value", 0)
                min_val = data.get("min_value", 0)
                mean_val = data.get("mean", 0)
                median_val = data.get("median", 0)
                html_content += f'<tr><td><strong>{metric}</strong></td><td>{max_val:,.2f}</td><td>{min_val:,.2f}</td><td>{mean_val:,.2f}</td><td>{median_val:,.2f}</td></tr>'
            html_content += '</table>'
        
        # Safe insights generation
        if business_insights:
            html_content += '<h3 class="section-title" style="margin-top: 40px;"><span>🔍</span> Business Intelligence</h3>'
            html_content += '<div class="insight-grid">'
            for insight in business_insights[:5]:
                html_content += f"""
                <div class="insight-card">
                    <div class="insight-icon">✨</div>
                    <div class="insight-content">{insight}</div>
                </div>"""
            html_content += '</div>'
        
        # Safe correlations generation
        if correlations:
            html_content += '<h3 class="section-title" style="margin-top: 40px;"><span>🔗</span> Key Relationships</h3>'
            for corr in correlations[:3]:
                try:
                    corr_value = corr.get("correlation", 0)
                    strength_class = "strong" if abs(corr_value) > 0.7 else "moderate" if abs(corr_value) > 0.3 else "weak"
                    strength_text = "Strong" if abs(corr_value) > 0.7 else "Moderate" if abs(corr_value) > 0.3 else "Weak"
                    html_content += f"""
                    <div class="correlation-item">
                        <div style="flex: 1;">
                            <strong>{corr.get("column1", "Variable 1")}</strong> ↔ 
                            <strong>{corr.get("column2", "Variable 2")}</strong>
                        </div>
                        <div>
                            <span class="correlation-strength {strength_class}">{strength_text}</span>
                            <div style="margin-top: 8px; font-weight: 600; font-size: 1.1rem;">r = {corr_value:.3f}</div>
                        </div>
                    </div>"""
                except Exception as e:
                    continue
        
        html_content += f"""</div>
    
    <div class="section">
        <h2 class="section-title"><span>✅</span> Data Quality Assessment</h2>
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-value">{missing_percentage:.1f}%</div>
                <div class="metric-label">Missing Values</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{duplicate_rows:,}</div>
                <div class="metric-label">Duplicate Records</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{outliers:,}</div>
                <div class="metric-label">Potential Outliers</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{inconsistencies:,}</div>
                <div class="metric-label">Data Inconsistencies</div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2 class="section-title"><span>📋</span> Executive Summary</h2>
        <div class="executive-summary">
            <p style="font-size: 1.2rem; margin-bottom: 25px; line-height: 1.8;">This comprehensive analysis of the <strong class="glow">{domain}</strong> dataset reveals valuable insights from <strong>{len(df):,}</strong> records across <strong>{len(df.columns):,}</strong> attributes. Key findings include:</p>
            
            <ul class="summary-points">
                <li><strong>{key_metrics_count}</strong> key performance metrics with detailed statistical analysis</li>
                <li><strong>{len(business_insights)}</strong> actionable business insights identified</li>
                <li><strong>{len(correlations)}</strong> significant data relationships discovered</li>
                <li>Overall data quality score of <strong>{quality_score:.1f}%</strong> with recommendations for improvement</li>
                <li>Advanced statistical analysis of key metrics and performance indicators</li>
            </ul>
        </div>
    </div>
    
    <div class="report-footer">
        <div style="font-size: 1.2rem; font-weight: 600;">Advanced Analytics Report | Confidential</div>
        <div class="footer-note">Generated by Data Insights Engine v3.0 • Dark Theme</div>
    </div>
</div>
</body>
</html>"""
        
        # Write the enhanced report
        report_path = 'professional_data_report_dark.html'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return report_path
    
    except Exception as e:
        print(f"Error generating HTML report: {str(e)}")
        # Fallback to simple report if error occurs
        simple_html = f"<html><body><h1>Report Generation Error</h1><p>{str(e)}</p></body></html>"
        with open('error_report.html', 'w') as f:
            f.write(simple_html)
        return 'error_report.html'
    
def reset_analysis() -> str:
    """Reset all analysis data"""
    global GLOBAL_DF, ANALYSIS_CACHE
    
    GLOBAL_DF = None
    ANALYSIS_CACHE = {
        'data': None,
        'insights': {},
        'metadata': {},
        'analysis_results': {},
        'visualizations': {},
        'business_context': {}
    }
    
    return "✅ Analysis data reset successfully."

# MAIN ASYNC FUNCTIONS - OPTIMIZED FOR SPEED
@function_tool()
async def load_and_analyze_excel() -> str:
    """
    Loads an Excel file, analyzes its contents using optimized parallel execution, 
    and caches the results including business context, data quality, and insights.
    """
    global GLOBAL_DF, ANALYSIS_CACHE
    
    try:
        loop = asyncio.get_event_loop()
        
        # File selection (runs on a separate thread)
        with ThreadPoolExecutor(max_workers=1) as executor:
            file_path = await loop.run_in_executor(executor, _open_file_dialog)
        
        if not file_path:
            return "No file selected for analysis."
        
        # Load data in thread
        with ThreadPoolExecutor(max_workers=1) as executor:
            df = await loop.run_in_executor(executor, _load_data_file, file_path)
        
        if df is None or df.empty:
            return "The selected file could not be loaded or is empty."
        
        GLOBAL_DF = df
        ANALYSIS_CACHE['data'] = df
        
        # Run analysis in parallel threads
        with ThreadPoolExecutor(max_workers=4) as executor:
            business_task = loop.run_in_executor(executor, _detect_business_context_fast, df)
            quality_task = loop.run_in_executor(executor, _analyze_data_quality_fast, df)
            
            business_context, quality_results = await asyncio.gather(business_task, quality_task)
            
            insights_task = loop.run_in_executor(executor, _find_key_insights_fast, df, business_context)
            insights = await insights_task
        
        ANALYSIS_CACHE.update({
            'business_context': business_context,
            'analysis_results': quality_results,
            'insights': insights,
            'metadata': {
                'analysis_timestamp': datetime.now().isoformat(),
                'file_path': file_path,
                'total_rows': len(df),
                'total_columns': len(df.columns)
            }
        })
        
        return (
            f"Analysis completed.\n"
            f"Dataset: {len(df):,} rows × {len(df.columns):,} columns\n"
            f"Detected Domain: {business_context['domain']}\n"
            f"Data Quality Score: {quality_results['quality_score']:.1f}%\n"
            f"Generated Insights: {len(insights['business_insights'])}"
        )
        
    except Exception as e:
        return f"Error during analysis: {str(e)}"


@function_tool()
async def get_analysis_report() -> str:
    """
    Generates and opens a detailed HTML report summarizing the analysis results,
    including business insights, data quality, and detected context.
    """
    global ANALYSIS_CACHE
    
    if ANALYSIS_CACHE['data'] is None:
        return "No data available. Please run load_and_analyze_excel() first."
    
    try:
        df = ANALYSIS_CACHE['data']
        insights = ANALYSIS_CACHE['insights']
        quality = ANALYSIS_CACHE['analysis_results']
        business_context = ANALYSIS_CACHE['business_context']
        
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=1) as executor:
            report_path = await loop.run_in_executor(
                executor, _generate_html_report_fast, df, insights, quality, business_context
            )
        
        webbrowser.open(f'file://{os.path.abspath(report_path)}')
        return f"Report generated and opened successfully at: {report_path}"
        
    except Exception as e:
        return f"Report generation failed: {str(e)}"


@function_tool()
async def get_analysis_status() -> str:
    """
    Returns the current status of the analysis, including data shape, domain,
    quality score, number of insights, and timestamp.
    """
    global ANALYSIS_CACHE
    
    if ANALYSIS_CACHE['data'] is None:
        return "No data loaded. Please perform analysis first."
    
    df = ANALYSIS_CACHE['data']
    business_context = ANALYSIS_CACHE['business_context']
    quality = ANALYSIS_CACHE['analysis_results']
    insights = ANALYSIS_CACHE['insights']
    
    return (
        f"Analysis Status\n"
        f"{'-'*40}\n"
        f"Rows × Columns: {len(df):,} × {len(df.columns):,}\n"
        f"Business Domain: {business_context['domain'].title()}\n"
        f"Quality Score: {quality['quality_score']:.1f}%\n"
        f"Key Metrics: {len(business_context['key_metrics'])}\n"
        f"Insights Generated: {len(insights['business_insights'])}\n"
        f"Last Run: {ANALYSIS_CACHE['metadata']['analysis_timestamp'][:19]}"
    )


@function_tool()
async def create_visualizations_chart() -> str:
    """
    Generates and saves visual charts based on the analyzed dataset and
    detected business context.
    """
    global ANALYSIS_CACHE
    
    if ANALYSIS_CACHE['data'] is None:
        return "No dataset available for visualization. Please analyze data first."
    
    try:
        df = ANALYSIS_CACHE['data']
        business_context = ANALYSIS_CACHE['business_context']
        
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=1) as executor:
            visualizations = await loop.run_in_executor(
                executor, _create_visualizations_fast, df, business_context
            )
        
        ANALYSIS_CACHE['visualizations'] = visualizations
        
        if visualizations:
            result = "Generated Visualizations:\n"
            for chart, path in visualizations.items():
                result += f"- {chart}: {path}\n"
            return result
        else:
            return "No visualizations were generated for this dataset."
            
    except Exception as e:
        return f"Visualization error: {str(e)}"


@function_tool()
async def get_top_insights() -> str:
    """
    Retrieves top business insights derived from the dataset analysis.
    """
    global ANALYSIS_CACHE
    
    insights = ANALYSIS_CACHE.get('insights', {}).get('business_insights', [])
    
    if not insights:
        return "No business insights available. Please run analysis first."
    
    result = "Top Business Insights\n" + "-" * 50 + "\n"
    for i, insight in enumerate(insights, 1):
        result += f"{i}. {insight}\n\n"
    
    return result.strip()


@function_tool()
async def get_data_summary() -> str:
    """
    Summarizes the dataset including structure, quality metrics, and key features.
    """
    global ANALYSIS_CACHE
    
    if ANALYSIS_CACHE['data'] is None:
        return "Data summary not available. Please run analysis first."
    
    df = ANALYSIS_CACHE['data']
    context = ANALYSIS_CACHE['business_context']
    quality = ANALYSIS_CACHE['analysis_results']
    
    return (
        f"Dataset Summary\n"
        f"{'-'*40}\n"
        f"Dimensions: {len(df):,} rows × {len(df.columns):,} columns\n"
        f"Domain: {context['domain'].title()}\n"
        f"Data Quality: {quality['quality_score']:.1f}%\n"
        f"Missing Values: {quality['missing_percentage']:.1f}%\n"
        f"Numeric Columns: {len(context['numeric_columns'])}\n"
        f"Categorical Columns: {len(context['categorical_columns'])}\n"
        f"Key Metrics Identified: {len(context['key_metrics'])}\n"
        f"Last Analyzed: {ANALYSIS_CACHE['metadata']['analysis_timestamp'][:19]}"
    )


@function_tool()
async def export_results(format_type: str = 'json') -> str:
    """
    Exports the analysis results in the specified format.
    Supported formats: 'json', 'html'
    """
    global ANALYSIS_CACHE
    
    if not ANALYSIS_CACHE['insights']:
        return "No results available to export. Please run the analysis first."
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    if format_type.lower() == 'json':
        filename = f'analysis_results_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': ANALYSIS_CACHE['metadata'],
                'business_context': ANALYSIS_CACHE['business_context'],
                'analysis_results': ANALYSIS_CACHE['analysis_results'],
                'insights': ANALYSIS_CACHE['insights'],
                'export_timestamp': datetime.now().isoformat()
            }, f, indent=2, default=str)
        
        return f"Analysis results exported to {filename}"
    
    elif format_type.lower() == 'html':
        return await get_analysis_report()
    
    return "Unsupported export format. Use either 'json' or 'html'."






@function_tool()
async def full_analysis_with_report() -> str:
    """
    Asks user to select an Excel file, performs full analysis, creates visualizations and generates report.
    Executes the complete end-to-end analysis including report generation.
    Suitable for comprehensive data assessment and export.
    """
    result = await load_and_analyze_excel()
    if "Error" in result or "No file" in result:
        return result
    
    await create_visualizations_chart()
    report_result = await get_analysis_report()
    summary = await get_data_summary()
    
    return f"{result}\n\n{report_result}\n\n{summary}"





import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog
import os
import sys
import warnings
from datetime import datetime
from scipy import stats
import matplotlib.patches as mpatches
import asyncio
# Add this at the top of your function
import matplotlib
matplotlib.use('Agg')  # Set non-interactive backend

warnings.filterwarnings('ignore')

def _create_file_dialog():
    """Advanced file dialog with multiple format support"""
    try:
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)
        root.title("Advanced Graph Creator")
        
        file_path = filedialog.askopenfilename(
            title="🔍 Select Your Data File",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx"),
                ("Excel (old)", "*.xls"),
                ("Text files", "*.txt"),
                ("All supported", "*.csv;*.xlsx;*.xls;*.txt"),
                ("All files", "*.*")
            ],
            initialdir=os.path.expanduser("~/Downloads")
        )
        
        root.destroy()
        return file_path
    except Exception as e:
        print(f"❌ File dialog error: {e}")
        return None

def _create_input_dialog(prompt, options=None, dialog_type="string"):
    """Advanced input dialog with validation"""
    try:
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)
        
        if options:
            options_str = "\n".join([f"• {opt}" for opt in options])
            full_prompt = f"{prompt}\n\n📋 Available options:\n{options_str}\n\n✏️ Enter your choice:"
        else:
            full_prompt = prompt
        
        if dialog_type == "string":
            result = simpledialog.askstring("📊 Graph Creator Input", full_prompt)
        elif dialog_type == "integer":
            result = simpledialog.askinteger("📊 Graph Creator Input", full_prompt)
        
        root.destroy()
        return result
    except Exception as e:
        print(f"❌ Input dialog error: {e}")
        return None

def _load_data_smart(file_path):
    """Smart data loading with multiple encoding support"""
    try:
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.csv' or file_ext == '.txt':
            # Try multiple encodings and separators
            encodings = ['utf-8', 'latin1', 'cp1252', 'iso-8859-1']
            separators = [',', ';', '\t', '|']
            
            for encoding in encodings:
                for sep in separators:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding, sep=sep)
                        if len(df.columns) > 1 and len(df) > 0:
                            print(f"✅ Data loaded: {encoding} encoding, '{sep}' separator")
                            return df
                    except:
                        continue
            
            # Last resort - try with Python engine
            df = pd.read_csv(file_path, engine='python', encoding='utf-8', sep=None)
            return df
            
        elif file_ext in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path, engine='openpyxl' if file_ext == '.xlsx' else 'xlrd')
            return df
        
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")
            
    except Exception as e:
        raise Exception(f"Data loading failed: {str(e)}")

def _analyze_data(df):
    """Advanced data analysis and column classification"""
    analysis = {
        'numeric_columns': [],
        'categorical_columns': [],
        'datetime_columns': [],
        'boolean_columns': [],
        'mixed_columns': []
    }
    
    for col in df.columns:
        dtype = df[col].dtype
        
        if pd.api.types.is_numeric_dtype(dtype):
            analysis['numeric_columns'].append(col)
        elif pd.api.types.is_datetime64_any_dtype(dtype):
            analysis['datetime_columns'].append(col)
        elif pd.api.types.is_bool_dtype(dtype):
            analysis['boolean_columns'].append(col)
        elif pd.api.types.is_object_dtype(dtype):
            # Check if it's actually numeric
            numeric_count = pd.to_numeric(df[col], errors='coerce').count()
            total_count = df[col].count()
            
            if numeric_count > total_count * 0.8:
                analysis['mixed_columns'].append(col)
            else:
                analysis['categorical_columns'].append(col)
        else:
            analysis['categorical_columns'].append(col)
    
    return analysis

def _create_advanced_styling(style, color_scheme):
    """Advanced styling configuration"""
    # Style mapping
    style_configs = {
        "default": {"style": "default", "colors": plt.cm.tab10},
        "seaborn": {"style": "seaborn-v0_8-darkgrid", "colors": plt.cm.Set2},
        "ggplot": {"style": "ggplot", "colors": plt.cm.Set1},
        "bmh": {"style": "bmh", "colors": plt.cm.Dark2},
        "classic": {"style": "classic", "colors": plt.cm.tab20},
        "dark": {"style": "dark_background", "colors": plt.cm.plasma}
    }
    
    # Apply style
    config = style_configs.get(style, style_configs["default"])
    try:
        plt.style.use(config["style"])
    except:
        plt.style.use("default")
    
    # Color scheme
    color_maps = {
        "auto": plt.cm.tab10,
        "viridis": plt.cm.viridis,
        "plasma": plt.cm.plasma,
        "coolwarm": plt.cm.coolwarm,
        "tab10": plt.cm.tab10,
        "pastel": plt.cm.Pastel1
    }
    
    colors = color_maps.get(color_scheme, color_maps["auto"])
    return colors

def _add_statistics(ax, df, x_column, y_column, graph_type, show_stats):
    """Add statistical information to graph"""
    if not show_stats or graph_type in ["pie", "heatmap"]:
        return
    
    stats_text = []
    
    if y_column and y_column in df.columns:
        data = df[y_column].dropna()
        if len(data) > 0:
            stats_text.append(f"Mean: {data.mean():.2f}")
            stats_text.append(f"Median: {data.median():.2f}")
            stats_text.append(f"Std: {data.std():.2f}")
            stats_text.append(f"Count: {len(data)}")
    
    if stats_text:
        textstr = '\n'.join(stats_text)
        props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
        ax.text(0.02, 0.98, textstr, transform=ax.transAxes, fontsize=9,
               verticalalignment='top', bbox=props)

def _create_graph_advanced(df, graph_type, x_column, y_column, colors, ax):
    """Advanced graph creation with all types"""
    
    if graph_type == "line":
        if df[x_column].dtype == 'object' or len(df) > 100:
            x_vals = range(len(df))
            line = ax.plot(x_vals, df[y_column], marker='o', linewidth=2.5, 
                          markersize=5, alpha=0.8, color=colors(0.1))
            ax.fill_between(x_vals, df[y_column], alpha=0.1, color=colors(0.1))
            
            # Smart x-axis labeling
            step = max(1, len(df) // 15)
            ax.set_xticks(x_vals[::step])
            ax.set_xticklabels(df[x_column].iloc[::step], rotation=45, ha='right')
        else:
            ax.plot(df[x_column], df[y_column], marker='o', linewidth=2.5, 
                   markersize=6, alpha=0.8, color=colors(0.1))
            ax.fill_between(df[x_column], df[y_column], alpha=0.1, color=colors(0.1))
    
    elif graph_type == "bar":
        if len(df) > 50:
            # Show top 50 values
            if y_column in df.select_dtypes(include=[np.number]).columns:
                df_plot = df.nlargest(50, y_column)
            else:
                df_plot = df.head(50)
        else:
            df_plot = df
        
        bars = ax.bar(range(len(df_plot)), df_plot[y_column], 
                     color=colors(np.linspace(0, 1, len(df_plot))), 
                     alpha=0.8, edgecolor='black', linewidth=0.5)
        
        ax.set_xticks(range(len(df_plot)))
        ax.set_xticklabels(df_plot[x_column], rotation=45, ha='right')
        
        # Add value labels on bars
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.1f}', ha='center', va='bottom', fontsize=8)
    
    elif graph_type == "scatter":
        scatter = ax.scatter(df[x_column], df[y_column], 
                           alpha=0.7, s=80, c=range(len(df)), 
                           cmap=colors, edgecolors='black', linewidth=0.5)
        
        # Add trend line
        if len(df) > 2:
            z = np.polyfit(df[x_column], df[y_column], 1)
            p = np.poly1d(z)
            ax.plot(df[x_column], p(df[x_column]), "r--", alpha=0.8, linewidth=2)
    
    elif graph_type == "histogram":
        data = df[x_column].dropna()
        n, bins, patches = ax.hist(data, bins=min(50, max(10, len(data)//20)), 
                                 alpha=0.7, color=colors(0.3), edgecolor='black')
        
        # Color bars by height
        for i, patch in enumerate(patches):
            patch.set_facecolor(colors(i / len(patches)))
        
        # Add normal distribution curve
        if len(data) > 10:
            mu, sigma = stats.norm.fit(data)
            x = np.linspace(data.min(), data.max(), 100)
            y = stats.norm.pdf(x, mu, sigma) * len(data) * (bins[1] - bins[0])
            ax.plot(x, y, 'r-', linewidth=2, alpha=0.8, label=f'Normal (μ={mu:.2f}, σ={sigma:.2f})')
            ax.legend()
    
    elif graph_type == "pie":
        if df[x_column].dtype == 'object':
            value_counts = df[x_column].value_counts().head(10)
            colors_pie = colors(np.linspace(0, 1, len(value_counts)))
            
            wedges, texts, autotexts = ax.pie(value_counts.values, 
                                             labels=value_counts.index,
                                             autopct='%1.1f%%', 
                                             startangle=90, 
                                             colors=colors_pie,
                                             explode=[0.05] * len(value_counts))
            
            # Enhance text
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
        else:
            raise ValueError("Pie chart requires categorical column")
    
    elif graph_type == "box":
        if df[x_column].dtype == 'object' and y_column:
            # Grouped box plot
            categories = df[x_column].unique()
            data_to_plot = [df[df[x_column] == cat][y_column].dropna() for cat in categories]
            
            bp = ax.boxplot(data_to_plot, labels=categories, patch_artist=True)
            
            # Color boxes
            for patch, color in zip(bp['boxes'], colors(np.linspace(0, 1, len(categories)))):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
        else:
            # Single box plot
            data = df[x_column].dropna()
            bp = ax.boxplot(data, patch_artist=True)
            bp['boxes'][0].set_facecolor(colors(0.3))
            bp['boxes'][0].set_alpha(0.7)
    
    elif graph_type == "heatmap":
        numeric_df = df.select_dtypes(include=[np.number])
        if numeric_df.empty:
            raise ValueError("Heatmap requires numeric columns")
        
        correlation_matrix = numeric_df.corr()
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        
        sns.heatmap(correlation_matrix, mask=mask, annot=True, 
                   cmap='RdYlBu_r', center=0, ax=ax, square=True,
                   linewidths=0.5, cbar_kws={"shrink": 0.8})
    
    elif graph_type == "area":
        if df[x_column].dtype == 'object':
            x_vals = range(len(df))
            ax.fill_between(x_vals, df[y_column], alpha=0.4, color=colors(0.3))
            ax.plot(x_vals, df[y_column], linewidth=3, color=colors(0.8))
            
            step = max(1, len(df) // 15)
            ax.set_xticks(x_vals[::step])
            ax.set_xticklabels(df[x_column].iloc[::step], rotation=45, ha='right')
        else:
            ax.fill_between(df[x_column], df[y_column], alpha=0.4, color=colors(0.3))
            ax.plot(df[x_column], df[y_column], linewidth=3, color=colors(0.8))
def create_advanced_graph(
    file_path: str = None,
    graph_type: str = "line",
    x_column: str = None,
    y_column: str = None,
    data_range: str = "full",
    data_limit: int = None,
    title: str = None,
    save_name: str = None,    
    style: str = "default",
    color_scheme: str = "auto",
    interactive_mode: bool = True,
    show_stats: bool = True,
    export_format: str = "png",
    dpi: int = 300,
    figsize: tuple = (12, 8)
) -> str:
    """
    🚀 ADVANCED GRAPH CREATOR - Complete Solution in One Method
    
    Features:
    - Smart file dialog with multiple format support
    - 8 different graph types with professional styling
    - Interactive column selection
    - Advanced data preprocessing
    - Statistical analysis overlay
    - Multiple export formats
    - Cross-platform compatibility
    
    Args:
        file_path (str): Path to data file (None = opens file dialog)
        graph_type (str): "line", "bar", "scatter", "histogram", "pie", "box", "heatmap", "area"
        x_column (str): X-axis column (None = auto-detect/ask user)
        y_column (str): Y-axis column (None = auto-detect/ask user)
        data_range (str): "full", "head", "tail", "custom"
        data_limit (int): Number of rows for head/tail/custom
        title (str): Graph title (None = auto-generate)
        save_name (str): Save filename (None = auto-generate)
        style (str): "default", "seaborn", "ggplot", "bmh", "classic", "dark"
        figsize (tuple): Figure size (width, height)
        color_scheme (str): "auto", "viridis", "plasma", "coolwarm", "tab10", "pastel"
        interactive_mode (bool): Enable GUI dialogs for user input
        show_stats (bool): Show statistical information on graph
        export_format (str): "png", "jpg", "pdf", "svg"
        dpi (int): Resolution for export (300 = high quality)
        
    Returns:
        str: Detailed status message with graph information
    """
    
    try:
        print("🚀 Advanced Graph Creator Starting...")
        
        # Step 1: File Selection
        if not file_path:
            print("📁 Opening advanced file dialog...")
            file_path = _create_file_dialog()
            
            if not file_path:
                return "❌ No file selected - Process cancelled"
            
            print(f"✅ File selected: {os.path.basename(file_path)}")
        
        # Validate file exists
        if not os.path.exists(file_path):
            return f"❌ File not found: {file_path}"
        
        # Step 2: Smart Data Loading
        print("📊 Loading data with smart detection...")
        df = _load_data_smart(file_path)
        
        if df.empty:
            return "❌ File is empty or contains no valid data"
        
        print(f"✅ Data loaded successfully: {df.shape[0]} rows × {df.shape[1]} columns")
        
        # Step 3: Data Analysis
        analysis = _analyze_data(df)
        print(f"🔍 Data Analysis:")
        print(f"   • Numeric columns: {len(analysis['numeric_columns'])}")
        print(f"   • Categorical columns: {len(analysis['categorical_columns'])}")
        print(f"   • DateTime columns: {len(analysis['datetime_columns'])}")
        
        # Step 4: Data Range Processing
        original_size = len(df)
        
        if data_range == "head" and data_limit:
            df = df.head(data_limit)
            print(f"📊 Using first {data_limit} rows")
        elif data_range == "tail" and data_limit:
            df = df.tail(data_limit)
            print(f"📊 Using last {data_limit} rows")
        elif data_range == "custom" and data_limit:
            df = df.sample(n=min(data_limit, len(df)), random_state=42)
            print(f"📊 Using random sample of {min(data_limit, len(df))} rows")
        
        # Step 5: Column Selection
        print("🎯 Column selection process...")
        
        # X-column selection
        if not x_column:
            if graph_type in ["histogram", "box"]:
                suitable_columns = analysis['numeric_columns'] + analysis['mixed_columns']
                if not suitable_columns:
                    return f"❌ {graph_type} requires numeric columns"
                
                x_column = suitable_columns[0]
                if interactive_mode and len(suitable_columns) > 1:
                    choice = _create_input_dialog(
                        f"Select column for {graph_type}:",
                        suitable_columns
                    )
                    if choice in suitable_columns:
                        x_column = choice
            else:
                x_column = df.columns[0]
                if interactive_mode:
                    choice = _create_input_dialog(
                        "Select X-axis column:",
                        list(df.columns)
                    )
                    if choice in df.columns:
                        x_column = choice
        
        # Y-column selection
        if not y_column and graph_type not in ["histogram", "pie", "box", "heatmap"]:
            suitable_columns = analysis['numeric_columns'] + analysis['mixed_columns']
            if not suitable_columns:
                return f"❌ {graph_type} requires numeric Y-axis column"
            
            y_column = suitable_columns[0] if x_column != suitable_columns[0] else (
                suitable_columns[1] if len(suitable_columns) > 1 else suitable_columns[0]
            )
            
            if interactive_mode:
                choice = _create_input_dialog(
                    "Select Y-axis column:",
                    suitable_columns
                )
                if choice in suitable_columns:
                    y_column = choice
        
        # Validate columns
        if x_column not in df.columns:
            return f"❌ Column '{x_column}' not found in data"
        if y_column and y_column not in df.columns:
            return f"❌ Column '{y_column}' not found in data"
        
        print(f"✅ Columns selected - X: {x_column}, Y: {y_column}")
        
        # Step 6: Advanced Styling
        colors = _create_advanced_styling(style, color_scheme)
        
        # Step 7: Create Graph
        print(f"🎨 Creating {graph_type} graph with advanced styling...")
        
        # Clear any existing plots
        plt.clf()
        plt.close('all')
        
        fig, ax = plt.subplots(figsize=figsize, facecolor='white')
        
        # Create the graph
        _create_graph_advanced(df, graph_type, x_column, y_column, colors, ax)
        
        # Step 8: Add Statistical Information
        if show_stats:
            _add_statistics(ax, df, x_column, y_column, graph_type, show_stats)
        
        # Step 9: Advanced Graph Customization
        if not title:
            title = f"{graph_type.title()} Analysis: {x_column}" + (f" vs {y_column}" if y_column else "")
        
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        
        if graph_type not in ["pie", "heatmap"]:
            ax.set_xlabel(x_column, fontsize=12, fontweight='bold')
            if y_column:
                ax.set_ylabel(y_column, fontsize=12, fontweight='bold')
            
            # Advanced grid
            ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)
            ax.set_axisbelow(True)
        
        # Improve layout
        plt.tight_layout()
        
        # Step 10: Save Graph
        if not save_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.splitext(os.path.basename(file_path))[0]
            save_name = f"{filename}_{graph_type}_{timestamp}.{export_format}"
        
        if not save_name.endswith(f'.{export_format}'):
            save_name += f'.{export_format}'
        
        # Save location
        downloads_path = os.path.join(os.path.expanduser("~"), "Downloads")
        if not os.path.exists(downloads_path):
            downloads_path = os.path.expanduser("~")
        
        full_save_path = os.path.join(downloads_path, save_name)
        
        # Save with format-specific settings
        save_params = {
            'dpi': dpi,
            'bbox_inches': 'tight',
            'facecolor': 'white',
            'edgecolor': 'none'
        }
        
        if export_format == 'pdf':
            save_params['format'] = 'pdf'
        elif export_format == 'svg':
            save_params['format'] = 'svg'
        
        plt.savefig(full_save_path, **save_params)
        print(f"💾 Graph saved: {full_save_path}")
        
        # Step 11: Display Graph
        plt.show()
        
        # Step 12: Auto-open file (cross-platform)
        try:
            if sys.platform.startswith('win'):
                os.startfile(full_save_path)
            elif sys.platform.startswith('darwin'):
                os.system(f'open "{full_save_path}"')
            else:
                os.system(f'xdg-open "{full_save_path}"')
        except:
            pass
        
        # Step 13: Generate detailed report
        report = f"""
🎉 GRAPH CREATION SUCCESSFUL!

📊 GRAPH DETAILS:
   • Type: {graph_type.title()}
   • Style: {style}
   • Color Scheme: {color_scheme}
   • Size: {figsize[0]}×{figsize[1]}
   • Statistics: {'Enabled' if show_stats else 'Disabled'}

📁 DATA INFORMATION:
   • File: {os.path.basename(file_path)}
   • Total Rows: {original_size:,}
   • Used Rows: {len(df):,}
   • Columns: {df.shape[1]}

📈 AXIS CONFIGURATION:
   • X-axis: {x_column}
   • Y-axis: {y_column if y_column else 'N/A'}

💾 EXPORT DETAILS:
   • Format: {export_format.upper()}
   • Resolution: {dpi} DPI
   • Filename: {save_name}
   • Location: {full_save_path}

🔍 DATA ANALYSIS:
   • Numeric Columns: {len(analysis['numeric_columns'])}
   • Categorical Columns: {len(analysis['categorical_columns'])}
   • DateTime Columns: {len(analysis['datetime_columns'])}
        """
        
        return report.strip()
        
    except Exception as e:
        error_msg = f"❌ Graph creation failed: {str(e)}"
        print(error_msg)
        return error_msg
    
    finally:
        plt.close('all')
# Main function with decorator and async - this is what you'll call from another class
@function_tool()
async def create_quick_advanced_graph(
    graph_type: str = "line",
    x_column: str = None,
    y_column: str = None,
    data_range: str = "full",
    data_limit: int = None,
    title: str = None,
    save_name: str = None,    
    style: str = "default",
    color_scheme: str = "auto",
    interactive_mode: bool = False,
    show_stats: bool = True,
    export_format: str = "png",
    dpi: int = 300
) -> str:
    """
    ⚡ QUICK ADVANCED GRAPH CREATOR - Fast One-Click Solution
    
    Similar to create_advanced_graph() but optimized for speed
    with minimal user interaction and smart defaults.
    
    Args:
        graph_type (str): Type of graph to create
        x_column (str): X-axis column name
        y_column (str): Y-axis column name
        data_range (str): Range of data to use
        data_limit (int): Limit number of rows
        title (str): Graph title
        save_name (str): Save filename
        style (str): Graph style
        color_scheme (str): Color scheme
        interactive_mode (bool): Interactive mode
        show_stats (bool): Show statistics
        export_format (str): Export format
        dpi (int): DPI for export
        
    Returns:
        str: Quick status report with essential graph details
    """
    
    try:
        print("⚡ Starting Quick Advanced Graph Creation...")
        
        # Execute with speed-optimized parameters
        result = create_advanced_graph(
            file_path=None,              # File dialog
            graph_type=graph_type,       # Graph type
            x_column=x_column,           # X column
            y_column=y_column,           # Y column
            data_range=data_range,       # Data range
            data_limit=data_limit,       # Data limit
            title=title,                 # Title
            save_name=save_name,         # Save name
            style=style,                 # Style             # Figure size
            color_scheme=color_scheme,   # Color scheme
            interactive_mode=interactive_mode,  # Interactive mode
            show_stats=show_stats,       # Statistics
            export_format=export_format, # Export format
            dpi=dpi                      # DPI
        )
        
        return result
        
    except Exception as e:
        error_msg = f"❌ Quick graph creation failed: {str(e)}"
        print(error_msg)
        return error_msg
    




    # Install required Python libraries


# Install the Nmap command-line tool
# Windows: Download the installer from https://nmap.org/download.html
# Linux (Debian/Ubuntu ): sudo apt-get install nmap```



# Add these imports at the top of your tools.py file if they aren't there
# Add these imports at the top of your tools.py file if they aren't there
# Add these imports at the top of your tools.py file if they aren't there


# ==============================================================================
# CORRECTED ALL-IN-ONE ADVANCED NETWORK SCANNER TOOL
# ==============================================================================
import nmap
import psutil
import socket
from mac_vendor_lookup import MacLookup
import asyncio
import tkinter as tk
from tkinter import Listbox, Scrollbar, Frame, Label, Button, SINGLE, END, ttk
from typing import List, Literal
import threading
import concurrent.futures
import time
import ipaddress
import json
import os
import queue

# ==============================================================================
# ULTRA-FAST ADVANCED INTERACTIVE NETWORK SCANNER
# Same structure as original but with massive speed improvements
# ==============================================================================

# Global cache for MAC vendors to avoid repeated API calls
_mac_cache = {}
_cache_file = "mac_vendor_cache.json"

# Queue for thread-safe UI updates
_ui_queue = queue.Queue()

def _load_mac_cache():
    """Load MAC vendor cache from file for instant lookups"""
    global _mac_cache
    try:
        if os.path.exists(_cache_file):
            with open(_cache_file, 'r') as f:
                _mac_cache = json.load(f)
    except Exception:
        _mac_cache = {}

def _save_mac_cache():
    """Save MAC vendor cache to file"""
    try:
        with open(_cache_file, 'w') as f:
            json.dump(_mac_cache, f)
    except Exception:
        pass

def _get_mac_vendor_fast(mac_address: str) -> str:
    """Ultra-fast MAC vendor lookup with caching"""
    if mac_address == 'N/A' or not mac_address:
        return "Unknown"
    
    # Check cache first for instant response
    if mac_address in _mac_cache:
        return _mac_cache[mac_address]
    
    try:
        vendor = MacLookup().lookup(mac_address)
        _mac_cache[mac_address] = vendor
        return vendor
    except Exception:
        _mac_cache[mac_address] = "Unknown"
        return "Unknown"

def _discover_network_ultra_fast() -> str:
    """Lightning-fast network discovery"""
    all_interfaces = psutil.net_if_addrs()
    all_stats = psutil.net_if_stats()
    
    # Priority interfaces for fastest detection
    priority_interfaces = ["Wi-Fi", "Ethernet", "wlan0", "eth0", "en0", "ens33"]
    
    for iface_name in priority_interfaces:
        if iface_name in all_interfaces and all_stats[iface_name].isup:
            for addr in all_interfaces[iface_name]:
                if addr.family == socket.AF_INET and not addr.address.startswith('127.'):
                    try:
                        net = ipaddress.IPv4Network(f"{addr.address}/{addr.netmask}", strict=False)
                        return str(net)
                    except Exception:
                        continue
    
    # Fallback to any active interface
    for iface_name, addrs in all_interfaces.items():
        if all_stats[iface_name].isup:
            for addr in addrs:
                if addr.family == socket.AF_INET and not addr.address.startswith('127.'):
                    try:
                        net = ipaddress.IPv4Network(f"{addr.address}/{addr.netmask}", strict=False)
                        return str(net)
                    except Exception:
                        continue
    
    return None

def _scan_devices_parallel(target_network: str, progress_callback=None):
    """Parallel device discovery for maximum speed"""
    try:
        nm = nmap.PortScanner()
        
        # Ultra-fast ping scan with maximum parallelism
        scan_args = '-sn -T5 --min-parallelism 100 --max-parallelism 256'
        
        if progress_callback:
            progress_callback("⚡ Scanning network at maximum speed...")
        
        nm.scan(hosts=target_network, arguments=scan_args)
        
        clients = []
        hosts = nm.all_hosts()
        
        if progress_callback:
            progress_callback(f"🔍 Found {len(hosts)} devices, getting vendor info...")
        
        # Parallel MAC vendor lookup for instant results
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            futures = []
            
            for host in hosts:
                mac = nm[host]['addresses'].get('mac', 'N/A')
                hostname = nm[host].hostname() if nm[host].hostname() else "N/A"
                
                client_data = {
                    'ip': host,
                    'mac': mac,
                    'hostname': hostname
                }
                
                # Submit MAC vendor lookup to thread pool
                future = executor.submit(_get_mac_vendor_fast, mac)
                futures.append((client_data, future))
            
            # Collect results with timeout for speed
            for client_data, future in futures:
                try:
                    client_data['manufacturer'] = future.result(timeout=1)
                except Exception:
                    client_data['manufacturer'] = "Unknown"
                clients.append(client_data)
        
        # Save cache after batch operations
        _save_mac_cache()
        
        return clients
        
    except Exception as e:
        print(f"Error in ultra-fast scan: {e}")
        return []

def _interactive_scan_workflow() -> str:
    """
    Ultra-fast interactive scanning workflow - same structure as original but 10x faster
    """
    # Load MAC cache at startup
    _load_mac_cache()
    
    def _create_selection_ui(title: str, prompt: str, options: List[str], show_progress: bool = False) -> str:
        result = ""
        progress_bar = None
        status_label = None
        root = None # Initialize root here

        def process_ui_queue():
            """Process UI updates from the queue in the main thread."""
            try:
                while True:
                    callback, args, kwargs = _ui_queue.get_nowait()
                    callback(*args, **kwargs)
            except queue.Empty:
                pass
            finally:
                if root: # Only reschedule if root still exists
                    root.after(100, process_ui_queue) # Check queue every 100ms

        def update_progress(message: str):
            """Thread-safe way to update progress label."""
            _ui_queue.put((lambda msg: status_label.config(text=msg), [message], {}))
        
        def on_select():
            nonlocal result
            try:
                selection = listbox.curselection()
                if selection:
                    result = options[selection[0]]
                else:
                    result = ""
            except IndexError:
                result = ""
            finally:
                if root: # Ensure root exists before quitting
                    root.quit()
                    root.destroy()

        def on_cancel():
            nonlocal result
            result = "cancel"
            if root: # Ensure root exists before quitting
                root.quit()
                root.destroy()
        
        def on_double_click(event):
            on_select()

        # Ultra-fast UI creation
        root = tk.Tk()
        root.title(f"⚡ {title}")
        root.attributes('-topmost', True)
        root.geometry("650x550")
        root.configure(bg='#1a1a1a')
        
        # Center window instantly
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Main frame with cyber theme
        main_frame = Frame(root, padx=20, pady=20, bg='#1a1a1a')
        main_frame.pack(fill="both", expand=True)
        
        # Title with neon effect
        title_label = Label(main_frame, text=f"⚡ {title}", font=('Consolas', 16, 'bold'), 
                           fg='#00ff41', bg='#1a1a1a')
        title_label.pack(pady=(0, 15))
        
        # Prompt
        prompt_label = Label(main_frame, text=prompt, wraplength=600, justify="left",
                           font=('Consolas', 11), fg='#ffffff', bg='#1a1a1a')
        prompt_label.pack(pady=(0, 15))
        
        # Progress bar for real-time feedback
        if show_progress:
            progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
            progress_bar.pack(fill="x", pady=(0, 10))
            progress_bar.start()
            
            status_label = Label(main_frame, text="🚀 Initializing ultra-fast scanner...", 
                               font=('Consolas', 10), fg='#00ffff', bg='#1a1a1a')
            status_label.pack(pady=(0, 15))
        
        # List frame with cyber styling
        list_frame = Frame(main_frame, bg='#1a1a1a')
        list_frame.pack(fill="both", expand=True)
        
        scrollbar = Scrollbar(list_frame, orient="vertical")
        listbox = Listbox(list_frame, yscrollcommand=scrollbar.set, selectmode=SINGLE, 
                         height=18, font=('Consolas', 10), bg='#0d1117', fg='#00ff41',
                         selectbackground='#1f6feb', selectforeground='#ffffff',
                         borderwidth=0, highlightthickness=0)
        scrollbar.config(command=listbox.yview)
        scrollbar.pack(side="right", fill="y")
        listbox.pack(side="left", fill="both", expand=True)
        
        # Double-click for instant selection
        listbox.bind('<Double-Button-1>', on_double_click)
        
        # Populate list
        for option in options:
            listbox.insert(END, option)
        
        # Button frame with neon styling
        button_frame = Frame(main_frame, bg='#1a1a1a')
        button_frame.pack(fill="x", pady=(20, 0))
        
        select_btn = Button(button_frame, text="⚡ SELECT & CONTINUE", command=on_select,
                          font=('Consolas', 11, 'bold'), bg='#238636', fg='#ffffff',
                          relief='flat', padx=25, pady=10, cursor='hand2')
        select_btn.pack(side="left", expand=True, padx=8)
        
        cancel_btn = Button(button_frame, text="❌ CANCEL", command=on_cancel,
                          font=('Consolas', 11, 'bold'), bg='#da3633', fg='#ffffff',
                          relief='flat', padx=25, pady=10, cursor='hand2')
        cancel_btn.pack(side="right", expand=True, padx=8)
        
        # Background device discovery for instant results
        if show_progress:
            def populate_devices_thread():
                """This function runs in a separate thread."""
                try:
                    target_network = _discover_network_ultra_fast()
                    if target_network:
                        devices = _scan_devices_parallel(target_network, update_progress)
                        _ui_queue.put((update_device_list, [devices], {}))
                    else:
                        _ui_queue.put((update_progress, ["❌ No active network found"], {}))
                except Exception as e:
                    _ui_queue.put((update_progress, [f"❌ Error: {str(e)}"], {}))
            
            def update_device_list(devices):
                """This function runs in the main thread."""
                listbox.delete(0, END)
                options.clear()
                
                if devices:
                    for device in devices:
                        option = f"{device['ip']:<16} │ {device['hostname']:<25} │ {device['manufacturer']}"
                        options.append(option)
                        listbox.insert(END, option)
                    
                    if progress_bar:
                        progress_bar.stop()
                        progress_bar.destroy()
                    if status_label:
                        status_label.config(text=f"✅ Found {len(devices)} devices - Double-click to select instantly!")
                else:
                    if status_label:
                        status_label.config(text="❌ No devices found on network")
            
            # Start ultra-fast discovery in a separate thread
            threading.Thread(target=populate_devices_thread, daemon=True).start()
        
        # Start processing the UI queue
        root.after(100, process_ui_queue)
        root.mainloop()
        return result

    try:
        # --- Part 1: Ultra-Fast Device Discovery ---
        print("🚀 [Thread] Step 1: Ultra-fast device discovery...")
        
        device_options = []
        selected_device_str = _create_selection_ui(
            "NETWORK DEVICE SCANNER", 
            "🔍 Discovering devices at maximum speed...", 
            device_options, 
            show_progress=True
        )

        if not selected_device_str or selected_device_str == "cancel":
            return "❌ Scan cancelled by user."
        
        target_ip = selected_device_str.split('│')[0].strip()
        print(f"   [Thread] Step 1 Complete: User selected {target_ip}")

        # --- Part 2: Lightning-Fast Scan Selection ---
        print("   [Thread] Step 2: Scan type selection...")
        
        scan_options = [
            "⚡ LIGHTNING SCAN      │ Top 100 ports    │ ~5 seconds",
            "🚀 TURBO SCAN         │ Top 1000 ports   │ ~15 seconds", 
            "🔍 QUICK SCAN         │ Common ports     │ ~30 seconds",
            "📊 STANDARD SCAN      │ Service detect   │ ~90 seconds",
            "🔥 INTENSE SCAN       │ Full analysis    │ ~3 minutes",
            "🎯 CUSTOM PORT SCAN   │ Specific ports   │ ~20 seconds",
            "🛡️ STEALTH SCAN       │ Undetectable     │ ~5 minutes"
        ]
        
        selected_scan_str = _create_selection_ui(
            "SCAN TYPE SELECTOR", 
            f"⚡ Choose scan intensity for {target_ip}:", 
            scan_options
        )

        if not selected_scan_str or selected_scan_str == "cancel":
            return "❌ Scan cancelled by user."
        
        # Ultra-optimized scan arguments
        scan_args_map = {
            "⚡ LIGHTNING SCAN": "-T5 --top-ports 100 --max-parallelism 200 --min-parallelism 50",
            "🚀 TURBO SCAN": "-T5 --top-ports 1000 --max-parallelism 150",
            "🔍 QUICK SCAN": "-T4 -F --max-parallelism 100",
            "📊 STANDARD SCAN": "-T4 -sV --version-intensity 5 --max-parallelism 50",
            "🔥 INTENSE SCAN": "-T4 -A -sV -O --max-parallelism 30",
            "🎯 CUSTOM PORT SCAN": "-T4 -p 21,22,23,25,53,80,110,135,139,443,445,993,995,1433,3389,5432,8080,8443",
            "🛡️ STEALTH SCAN": "-sS -T2 -f --scan-delay 1s"
        }
        
        scan_name = selected_scan_str.split('│')[0].strip()
        scan_args = None
        
        for key, args in scan_args_map.items():
            if selected_scan_str.startswith(key):
                scan_args = args
                break
        
        if not scan_args:
            scan_args = "-T4 -sV"
        
        print(f"   [Thread] Step 2 Complete: User selected '{scan_name}'")

        # --- Part 3: Execute Ultra-Fast Scan ---
        print(f"   [Thread] Step 3: Executing '{scan_name}' on {target_ip}...")
        
        start_time = time.time()
        nm = nmap.PortScanner()
        nm.scan(target_ip, arguments=scan_args)
        scan_duration = time.time() - start_time
        
        if target_ip not in nm.all_hosts():
            return f"❌ Could not get scan results for {target_ip}"

        # --- Part 4: Generate Lightning-Fast Report ---
        host_data = nm[target_ip]
        
        report = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    ⚡ ULTRA-FAST NETWORK SECURITY AUDIT ⚡                   ║
╚══════════════════════════════════════════════════════════════════════════════╝

🎯 TARGET: {target_ip}
⚡ SCAN: {scan_name}
⏱️  DURATION: {scan_duration:.2f} seconds
📅 TIME: {time.strftime('%Y-%m-%d %H:%M:%S')}

📋 HOST INFORMATION:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
• Hostname: {host_data.hostname() or 'N/A'}
• Status: {host_data.state().upper()}
• MAC: {host_data.get('addresses', {}).get('mac', 'N/A')}
"""

        # OS Detection
        if 'osmatch' in host_data and host_data['osmatch']:
            best_os = host_data['osmatch'][0]
            report += f"• OS: {best_os['name']} ({best_os['accuracy']}% confidence)\n"
        
        # Ultra-fast port analysis
        report += f"""
🔍 PORT ANALYSIS:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""
        
        open_ports = []
        total_ports = 0
        
        for proto in host_data.all_protocols():
            for port in sorted(host_data[proto].keys()):
                total_ports += 1
                service = host_data[proto][port]
                if service['state'] == 'open':
                    port_info = {
                        'port': port,
                        'service': service.get('name', 'unknown'),
                        'product': service.get('product', ''),
                        'version': service.get('version', '')
                    }
                    open_ports.append(port_info)
        
        if open_ports:
            report += f"🟢 OPEN PORTS ({len(open_ports)} found):\n"
            for port_info in open_ports:
                service_detail = f"{port_info['product']} {port_info['version']}".strip()
                if service_detail:
                    service_detail = f" ({service_detail})"
                
                report += f"   ├─ {port_info['port']:<6} │ {port_info['service']:<15}{service_detail}\n"
        else:
            report += "🟢 OPEN PORTS: None detected\n"
        
        # Lightning-fast security assessment
        high_risk_ports = [21, 23, 135, 139, 445, 1433, 3389]
        risk_ports_found = [p['port'] for p in open_ports if p['port'] in high_risk_ports]
        
        report += f"""
🛡️  SECURITY ASSESSMENT:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
• Risk Level: {"HIGH" if risk_ports_found else "MEDIUM" if open_ports else "LOW"}
• Open Services: {len(open_ports)}
• High-Risk Ports: {len(risk_ports_found)}
"""
        
        if risk_ports_found:
            report += f"• ⚠️  Critical: Ports {risk_ports_found} require immediate attention\n"
        
        # Performance metrics
        ports_per_second = total_ports / scan_duration if scan_duration > 0 else 0
        
        report += f"""
📊 PERFORMANCE METRICS:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
• Scan Speed: {ports_per_second:.1f} ports/second
• Total Ports: {total_ports}
• Efficiency: ULTRA-FAST ⚡
• Cache Hits: {len(_mac_cache)} MAC vendors cached

╚══════════════════════════════════════════════════════════════════════════════╝
"""
        
        print(f"   [Thread] Step 3 Complete: Ultra-fast report generated in {scan_duration:.2f}s")
        return report

    except Exception as e:
        return f"❌ Critical error in ultra-fast scanner: {str(e)}"

@function_tool()
async def advanced_network_scan() -> str:
    """
    Performs an ultra-fast, interactive network security audit with advanced features.
    Same structure as original but with massive speed improvements and modern UI.
    """
    print("🚀 Agent dispatching ultra-fast interactive network scanner...")
    
    loop = asyncio.get_event_loop()
    
    final_report = await loop.run_in_executor(
        None,
        _interactive_scan_workflow
    )
    
    print("✅ Ultra-fast scanner completed successfully!")
    return final_report

# Main execution
if __name__ == "__main__":
    # For direct execution
    result = asyncio.run(advanced_network_scan())
    print(result)

# ================================
# MEMORY SYSTEM FUNCTIONS
# ================================

@function_tool()
async def remember_user_info(key: str, value: str, category: str = "personal") -> str:
    """
    Store user information in long-term memory for future reference.

    Args:
        key (str): The information key (e.g., "favorite_color", "birthday", "work_schedule")
        value (str): The information value
        category (str): Category for organization (personal, work, preferences, etc.)

    Returns:
        str: Confirmation message in Hindi/English

    Examples:
        - remember_user_info("favorite_color", "blue", "preferences")
        - remember_user_info("birthday", "1990-07-19", "personal")
        - remember_user_info("work_time", "9 AM to 6 PM", "work")
    """
    try:
        if not MEMORY_AVAILABLE:
            return "❌ मेमोरी सिस्टम उपलब्ध नहीं है।"

        success = await remember_preference(key, value)

        if success:
            return f"✅ याद रख लिया: {key} = {value} (श्रेणी: {category})"
        else:
            return f"❌ जानकारी सेव करने में समस्या आई।"

    except Exception as e:
        logger.error(f"Remember user info error: {e}")
        return f"❌ मेमोरी में सेव करने में त्रुटि: {str(e)}"

@function_tool()
async def recall_user_info(key: str) -> str:
    """
    Retrieve stored user information from long-term memory.

    Args:
        key (str): The information key to retrieve

    Returns:
        str: The stored information or not found message

    Examples:
        - recall_user_info("favorite_color")
        - recall_user_info("birthday")
        - recall_user_info("work_schedule")
    """
    try:
        if not MEMORY_AVAILABLE:
            return "❌ मेमोरी सिस्टम उपलब्ध नहीं है।"

        value = await recall_preference(key)

        if value:
            return f"📝 {key}: {value}"
        else:
            return f"❌ '{key}' की जानकारी नहीं मिली।"

    except Exception as e:
        logger.error(f"Recall user info error: {e}")
        return f"❌ मेमोरी से पढ़ने में त्रुटि: {str(e)}"

@function_tool()
async def add_personal_reminder(title: str, date: str, time: str = None, description: str = None) -> str:
    """
    Add a personal reminder to ZARA's memory system.

    Args:
        title (str): Reminder title
        date (str): Date in YYYY-MM-DD format
        time (str): Time in HH:MM format (optional)
        description (str): Additional details (optional)

    Returns:
        str: Confirmation message with reminder ID

    Examples:
        - add_personal_reminder("Doctor appointment", "2024-08-20", "10:30", "Annual checkup")
        - add_personal_reminder("Birthday party", "2024-08-25", "18:00")
        - add_personal_reminder("Project deadline", "2024-08-30")
    """
    try:
        if not MEMORY_AVAILABLE:
            return "❌ मेमोरी सिस्टम उपलब्ध नहीं है।"

        reminder_id = await add_reminder(title, date, time, description)

        if reminder_id > 0:
            time_text = f" {time} बजे" if time else ""
            return f"✅ रिमाइंडर सेट किया गया (ID: {reminder_id})\n📅 {date}{time_text}\n📝 {title}"
        else:
            return "❌ रिमाइंडर सेट करने में समस्या आई।"

    except Exception as e:
        logger.error(f"Add reminder error: {e}")
        return f"❌ रिमाइंडर सेट करने में त्रुटि: {str(e)}"

@function_tool()
async def get_conversation_history(limit: int = 5) -> str:
    """
    Retrieve recent conversation history from memory.

    Args:
        limit (int): Number of recent conversations to retrieve (default: 5)

    Returns:
        str: Formatted conversation history

    This helps ZARA maintain context across sessions.
    """
    try:
        if not MEMORY_AVAILABLE:
            return "❌ मेमोरी सिस्टम उपलब्ध नहीं है।"

        conversations = await get_recent_context(limit)

        if not conversations:
            return "कोई पुरानी बातचीत नहीं मिली।"

        history_parts = []
        for i, conv in enumerate(conversations, 1):
            timestamp = conv['timestamp']
            user_msg = conv['user_message'][:100] + "..." if len(conv['user_message']) > 100 else conv['user_message']
            assistant_msg = conv['assistant_response'][:100] + "..." if len(conv['assistant_response']) > 100 else conv['assistant_response']

            history_parts.append(f"{i}. {timestamp}")
            history_parts.append(f"   👤 User: {user_msg}")
            history_parts.append(f"   🤖 ZARA: {assistant_msg}")
            if conv['tools_used']:
                history_parts.append(f"   🔧 Tools: {', '.join(conv['tools_used'])}")
            history_parts.append("")

        return "📚 हाल की बातचीत:\n" + "\n".join(history_parts)

    except Exception as e:
        logger.error(f"Get conversation history error: {e}")
        return f"❌ बातचीत का इतिहास लाने में त्रुटि: {str(e)}"

@function_tool()
async def get_memory_statistics() -> str:
    """
    Get statistics about ZARA's memory system usage.

    Returns:
        str: Formatted memory statistics

    Shows how much data ZARA has stored and learned.
    """
    try:
        if not MEMORY_AVAILABLE:
            return "❌ मेमोरी सिस्टम उपलब्ध नहीं है।"

        stats = await memory_system.get_memory_stats()

        if not stats:
            return "❌ मेमोरी आंकड़े प्राप्त करने में समस्या।"

        stats_text = [
            "📊 ZARA मेमोरी सिस्टम आंकड़े:",
            f"💬 कुल बातचीत: {stats.get('total_conversations', 0)}",
            f"📅 आज की बातचीत: {stats.get('todays_conversations', 0)}",
            f"⚙️ उपयोगकर्ता प्राथमिकताएं: {stats.get('user_preferences', 0)}",
            f"⏰ सक्रिय रिमाइंडर: {stats.get('active_reminders', 0)}",
            f"🧠 सीखे गए पैटर्न: {stats.get('learning_patterns', 0)}",
            f"💾 डेटाबेस साइज़: {stats.get('db_size_mb', 0)} MB"
        ]

        return "\n".join(stats_text)

    except Exception as e:
        logger.error(f"Get memory statistics error: {e}")
        return f"❌ मेमोरी आंकड़े प्राप्त करने में त्रुटि: {str(e)}"


# ==========================================
# 🖥️ LIVE SCREEN MONITORING & MOUSE CONTROL
# ==========================================

@function_tool()
async def capture_live_screen_basic() -> str:
    """
    📸 Basic screen capture without OCR dependencies

    Returns:
        str: Basic screen information

    Features:
        - Simple screen capture
        - Basic analysis without text detection
        - No external dependencies required
    """
    import pyautogui
    import asyncio
    from datetime import datetime

    try:
        print("📸 Capturing screen (basic mode)...")

        # Take screenshot
        screenshot = await asyncio.to_thread(pyautogui.screenshot)
        screen_width, screen_height = screenshot.size

        # Save screenshot with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = f"images/screen_basic_{timestamp}.png"
        screenshot.save(screenshot_path)

        return (f"✅ Screen captured successfully!\n"
                f"📏 Resolution: {screen_width}x{screen_height}\n"
                f"💾 Saved as: {screenshot_path}\n"
                f"⏰ Timestamp: {timestamp}")

    except Exception as e:
        return f"❌ Basic screen capture failed: {str(e)}"


@function_tool()
async def capture_live_screen(analyze_content: bool = True, save_screenshot: bool = False) -> str:
    """
    📸 Captures live screen and analyzes what's currently visible

    Args:
        analyze_content: Whether to analyze screen content with AI
        save_screenshot: Whether to save screenshot to file

    Returns:
        str: Screen analysis description

    Features:
        - Real-time screen capture
        - AI-powered content analysis
        - Object and text detection
        - Activity monitoring
    """
    import pyautogui
    import cv2
    import numpy as np
    import asyncio
    from datetime import datetime
    import os

    try:
        print("📸 Capturing live screen...")

        # Take screenshot
        screenshot = await asyncio.to_thread(pyautogui.screenshot)
        screenshot_np = np.array(screenshot)

        # Get screen dimensions
        screen_width, screen_height = screenshot.size

        # Save screenshot if requested
        screenshot_path = None
        if save_screenshot:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = f"images/screen_capture_{timestamp}.png"
            screenshot.save(screenshot_path)

        if not analyze_content:
            return f"✅ Screen captured ({screen_width}x{screen_height})" + (f" and saved to {screenshot_path}" if screenshot_path else "")

        # Convert for OpenCV processing
        image = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Basic screen analysis without OCR dependency
        analysis = []
        analysis.append(f"🖥️ Screen Resolution: {screen_width}x{screen_height}")

        # Detect UI elements (buttons, windows, etc.) using OpenCV
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Filter contours by size to find UI elements
        ui_elements = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 100 < area < 50000:  # Reasonable UI element size
                x, y, w, h = cv2.boundingRect(contour)
                ui_elements.append((x, y, w, h))

        analysis.append(f"🔲 UI Elements Detected: {len(ui_elements)}")

        # Color analysis
        pixels = screenshot_np.reshape(-1, 3)
        unique_colors, counts = np.unique(pixels, axis=0, return_counts=True)
        top_colors = unique_colors[np.argsort(counts)[-5:]]  # Top 5 colors

        analysis.append(f"🎨 Dominant Colors: {len(top_colors)} detected")

        # Try OCR text detection if available
        if TESSERACT_AVAILABLE:
            try:
                import pytesseract

                # Get text from screen
                text_data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)
                detected_text = []

                for i in range(len(text_data['text'])):
                    if int(text_data['conf'][i]) > 30:  # Confidence threshold
                        text = text_data['text'][i].strip()
                        if text:
                            detected_text.append(text)

                if detected_text:
                    analysis.append(f"📝 Text Elements Found: {len(detected_text)}")
                    # Show first few text elements
                    sample_text = detected_text[:5]
                    analysis.append(f"📄 Sample Text: {', '.join(sample_text)}")
                else:
                    analysis.append("📝 No readable text detected on screen")

            except Exception as ocr_error:
                analysis.append(f"⚠️ OCR Error: {str(ocr_error)}")
        else:
            analysis.append("⚠️ Tesseract OCR not available - Run 'python install_tesseract.py' to install")

        result = "\n".join(analysis)

        if screenshot_path:
            result += f"\n💾 Screenshot saved: {screenshot_path}"

        return result

    except Exception as e:
        return f"❌ Screen capture failed: {str(e)}"


@function_tool()
async def move_mouse_to_position(x: int, y: int, duration: float = 0.5, click: bool = False) -> str:
    """
    🖱️ Moves mouse cursor to specific screen coordinates

    Args:
        x: X coordinate (pixels from left)
        y: Y coordinate (pixels from top)
        duration: Movement duration in seconds
        click: Whether to click after moving

    Returns:
        str: Movement confirmation

    Features:
        - Smooth mouse movement
        - Coordinate validation
        - Optional clicking
        - Position tracking
    """
    import pyautogui
    import asyncio

    try:
        # Get screen dimensions for validation
        screen_width, screen_height = await asyncio.to_thread(pyautogui.size)

        # Validate coordinates
        if x < 0 or x > screen_width or y < 0 or y > screen_height:
            return f"❌ Invalid coordinates: ({x}, {y}). Screen size: {screen_width}x{screen_height}"

        # Get current position
        current_pos = await asyncio.to_thread(pyautogui.position)

        print(f"🖱️ Moving mouse from ({current_pos.x}, {current_pos.y}) to ({x}, {y})")

        # Move mouse smoothly
        await asyncio.to_thread(pyautogui.moveTo, x, y, duration=duration)

        # Click if requested
        if click:
            await asyncio.to_thread(pyautogui.click)
            return f"✅ Mouse moved to ({x}, {y}) and clicked"
        else:
            return f"✅ Mouse moved to ({x}, {y})"

    except Exception as e:
        return f"❌ Mouse movement failed: {str(e)}"


@function_tool()
async def get_mouse_position() -> str:
    """
    📍 Gets current mouse cursor position

    Returns:
        str: Current mouse coordinates and screen info
    """
    import pyautogui
    import asyncio

    try:
        # Get current position
        pos = await asyncio.to_thread(pyautogui.position)

        # Get screen dimensions
        screen_width, screen_height = await asyncio.to_thread(pyautogui.size)

        # Calculate relative position (percentage)
        x_percent = round((pos.x / screen_width) * 100, 1)
        y_percent = round((pos.y / screen_height) * 100, 1)

        return (f"🖱️ Mouse Position:\n"
                f"📍 Coordinates: ({pos.x}, {pos.y})\n"
                f"📏 Screen Size: {screen_width}x{screen_height}\n"
                f"📊 Relative Position: {x_percent}% from left, {y_percent}% from top")

    except Exception as e:
        return f"❌ Failed to get mouse position: {str(e)}"


@function_tool()
async def analyze_screen_activity(duration_seconds: int = 5, sensitivity: int = 30) -> str:
    """
    👁️ Monitors screen for changes and activity over time

    Args:
        duration_seconds: How long to monitor (1-30 seconds)
        sensitivity: Change detection sensitivity (1-100)

    Returns:
        str: Activity analysis report

    Features:
        - Motion detection
        - Change tracking
        - Activity hotspots
        - Real-time monitoring
    """
    import pyautogui
    import cv2
    import numpy as np
    import asyncio
    from datetime import datetime

    try:
        # Validate duration
        duration_seconds = max(1, min(30, duration_seconds))
        sensitivity = max(1, min(100, sensitivity))

        print(f"👁️ Starting screen activity monitoring for {duration_seconds} seconds...")

        # Take initial screenshot
        initial_screenshot = await asyncio.to_thread(pyautogui.screenshot)
        initial_frame = cv2.cvtColor(np.array(initial_screenshot), cv2.COLOR_RGB2BGR)
        initial_gray = cv2.cvtColor(initial_frame, cv2.COLOR_BGR2GRAY)

        changes_detected = 0
        activity_regions = []
        start_time = datetime.now()

        # Monitor for specified duration
        for i in range(duration_seconds):
            await asyncio.sleep(1)

            # Take new screenshot
            current_screenshot = await asyncio.to_thread(pyautogui.screenshot)
            current_frame = cv2.cvtColor(np.array(current_screenshot), cv2.COLOR_RGB2BGR)
            current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)

            # Calculate difference
            diff = cv2.absdiff(initial_gray, current_gray)

            # Apply threshold based on sensitivity
            threshold_value = 255 - (sensitivity * 2.55)
            _, thresh = cv2.threshold(diff, threshold_value, 255, cv2.THRESH_BINARY)

            # Find contours of changed areas
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Count significant changes
            frame_changes = 0
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 100:  # Minimum area for significant change
                    frame_changes += 1
                    x, y, w, h = cv2.boundingRect(contour)
                    activity_regions.append((x, y, w, h))

            if frame_changes > 0:
                changes_detected += frame_changes
                print(f"📊 Second {i+1}: {frame_changes} changes detected")

            # Update reference frame for next comparison
            initial_gray = current_gray

        end_time = datetime.now()
        monitoring_duration = (end_time - start_time).total_seconds()

        # Analyze activity patterns
        if activity_regions:
            # Find most active regions
            region_counts = {}
            for x, y, w, h in activity_regions:
                region_key = f"{x//100}_{y//100}"  # Group by 100px regions
                region_counts[region_key] = region_counts.get(region_key, 0) + 1

            most_active = max(region_counts.items(), key=lambda x: x[1])
            hotspot_info = f"🔥 Most Active Region: Grid {most_active[0]} ({most_active[1]} changes)"
        else:
            hotspot_info = "😴 No significant activity detected"

        # Generate report
        activity_level = "High" if changes_detected > 20 else "Medium" if changes_detected > 5 else "Low"

        report = (f"👁️ Screen Activity Analysis Complete\n"
                 f"⏱️ Monitoring Duration: {monitoring_duration:.1f} seconds\n"
                 f"📊 Total Changes Detected: {changes_detected}\n"
                 f"📈 Activity Level: {activity_level}\n"
                 f"🎯 Sensitivity Used: {sensitivity}%\n"
                 f"{hotspot_info}")

        return report

    except Exception as e:
        return f"❌ Screen activity monitoring failed: {str(e)}"


@function_tool()
async def monitor_screen_changes(target_area: str = "full", threshold: int = 50) -> str:
    """
    🔍 Detects specific changes in screen regions

    Args:
        target_area: Area to monitor ("full", "top", "bottom", "left", "right", "center")
        threshold: Change sensitivity (1-100)

    Returns:
        str: Change detection results

    Features:
        - Region-specific monitoring
        - Change quantification
        - Instant detection
        - Area analysis
    """
    import pyautogui
    import cv2
    import numpy as np
    import asyncio

    try:
        print(f"🔍 Monitoring screen changes in '{target_area}' area...")

        # Take initial screenshot
        screenshot1 = await asyncio.to_thread(pyautogui.screenshot)
        frame1 = cv2.cvtColor(np.array(screenshot1), cv2.COLOR_RGB2BGR)

        # Wait a moment
        await asyncio.sleep(2)

        # Take second screenshot
        screenshot2 = await asyncio.to_thread(pyautogui.screenshot)
        frame2 = cv2.cvtColor(np.array(screenshot2), cv2.COLOR_RGB2BGR)

        # Get screen dimensions
        height, width = frame1.shape[:2]

        # Define monitoring area
        if target_area == "top":
            roi1 = frame1[0:height//3, :]
            roi2 = frame2[0:height//3, :]
        elif target_area == "bottom":
            roi1 = frame1[2*height//3:height, :]
            roi2 = frame2[2*height//3:height, :]
        elif target_area == "left":
            roi1 = frame1[:, 0:width//3]
            roi2 = frame2[:, 0:width//3]
        elif target_area == "right":
            roi1 = frame1[:, 2*width//3:width]
            roi2 = frame2[:, 2*width//3:width]
        elif target_area == "center":
            roi1 = frame1[height//4:3*height//4, width//4:3*width//4]
            roi2 = frame2[height//4:3*height//4, width//4:3*width//4]
        else:  # full screen
            roi1 = frame1
            roi2 = frame2

        # Convert to grayscale
        gray1 = cv2.cvtColor(roi1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(roi2, cv2.COLOR_BGR2GRAY)

        # Calculate difference
        diff = cv2.absdiff(gray1, gray2)

        # Apply threshold
        threshold_value = 255 - (threshold * 2.55)
        _, thresh = cv2.threshold(diff, threshold_value, 255, cv2.THRESH_BINARY)

        # Calculate change percentage
        total_pixels = thresh.shape[0] * thresh.shape[1]
        changed_pixels = cv2.countNonZero(thresh)
        change_percentage = (changed_pixels / total_pixels) * 100

        # Find contours of changed areas
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        significant_changes = len([c for c in contours if cv2.contourArea(c) > 50])

        # Determine change level
        if change_percentage > 10:
            change_level = "Major"
        elif change_percentage > 2:
            change_level = "Moderate"
        elif change_percentage > 0.1:
            change_level = "Minor"
        else:
            change_level = "None"

        result = (f"🔍 Screen Change Detection Results\n"
                 f"📍 Monitored Area: {target_area.title()}\n"
                 f"📊 Change Percentage: {change_percentage:.2f}%\n"
                 f"📈 Change Level: {change_level}\n"
                 f"🎯 Significant Changes: {significant_changes}\n"
                 f"⚙️ Sensitivity: {threshold}%")

        return result

    except Exception as e:
        return f"❌ Screen change monitoring failed: {str(e)}"


# ==========================================
# 🔄 CONTINUOUS SCREEN MONITORING SYSTEM
# ==========================================

import threading
import queue
import hashlib
from dataclasses import dataclass
from typing import Dict, List, Optional, Set
import json
import re

@dataclass
class UIElement:
    """Represents a UI element detected on screen"""
    type: str  # button, textbox, link, image, etc.
    text: str
    position: tuple  # (x, y, width, height)
    confidence: float
    attributes: dict

@dataclass
class ScreenContext:
    """Current screen context and state"""
    timestamp: str
    application: str
    window_title: str
    ui_elements: List[UIElement]
    visible_text: List[str]
    screen_hash: str
    change_areas: List[tuple]

class ScreenMonitoringRules:
    """Privacy and performance rules for screen monitoring"""

    def __init__(self):
        self.privacy_keywords = {
            'password', 'pin', 'ssn', 'credit card', 'bank account',
            'social security', 'passport', 'license', 'confidential',
            'secret', 'private', 'personal', 'sensitive'
        }

        self.blocked_applications = {
            'banking', 'wallet', 'password manager', 'private browsing'
        }

        self.monitoring_rules = {
            'max_fps': 2,  # Maximum 2 captures per second
            'min_change_threshold': 5,  # Minimum 5% change to process
            'max_memory_mb': 100,  # Maximum 100MB memory usage
            'max_history_items': 50,  # Keep last 50 screen states
            'ocr_confidence_threshold': 60,  # Minimum OCR confidence
            'privacy_mode': True,  # Enable privacy filtering
            'smart_caching': True,  # Enable intelligent caching
        }

    def should_monitor_application(self, app_name: str, window_title: str) -> bool:
        """Check if application should be monitored"""
        app_lower = app_name.lower()
        title_lower = window_title.lower()

        # Block sensitive applications
        for blocked in self.blocked_applications:
            if blocked in app_lower or blocked in title_lower:
                return False

        # Block incognito/private browsing
        if 'incognito' in title_lower or 'private' in title_lower:
            return False

        return True

    def filter_sensitive_text(self, text: str) -> str:
        """Filter out sensitive information from text"""
        if not self.monitoring_rules['privacy_mode']:
            return text

        filtered_text = text

        # Mask potential passwords (sequences of special chars)
        filtered_text = re.sub(r'[•*]{3,}', '[HIDDEN]', filtered_text)

        # Mask credit card numbers
        filtered_text = re.sub(r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b', '[CARD-HIDDEN]', filtered_text)

        # Mask SSN patterns
        filtered_text = re.sub(r'\b\d{3}-\d{2}-\d{4}\b', '[SSN-HIDDEN]', filtered_text)

        # Check for privacy keywords
        for keyword in self.privacy_keywords:
            if keyword in filtered_text.lower():
                # Mask the line containing sensitive keywords
                lines = filtered_text.split('\n')
                filtered_lines = []
                for line in lines:
                    if keyword in line.lower():
                        filtered_lines.append('[SENSITIVE-CONTENT-HIDDEN]')
                    else:
                        filtered_lines.append(line)
                filtered_text = '\n'.join(filtered_lines)

        return filtered_text

class ContinuousScreenMonitor:
    """Advanced continuous screen monitoring system"""

    def __init__(self):
        self.is_running = False
        self.monitor_thread = None
        self.screen_queue = queue.Queue(maxsize=10)
        self.current_context: Optional[ScreenContext] = None
        self.screen_history: List[ScreenContext] = []
        self.rules = ScreenMonitoringRules()
        self.last_screen_hash = ""
        self.element_cache = {}

    def start_monitoring(self):
        """Start continuous screen monitoring"""
        if self.is_running:
            return "⚠️ Screen monitoring is already running"

        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()

        return "✅ Continuous screen monitoring started"

    def stop_monitoring(self):
        """Stop continuous screen monitoring"""
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        return "🛑 Screen monitoring stopped"

    def _monitor_loop(self):
        """Main monitoring loop"""
        import time

        while self.is_running:
            try:
                # Capture and analyze screen
                context = self._capture_and_analyze()
                if context:
                    self.current_context = context
                    self._update_history(context)

                # Sleep based on FPS setting
                sleep_time = 1.0 / self.rules.monitoring_rules['max_fps']
                time.sleep(sleep_time)

            except Exception as e:
                print(f"⚠️ Monitor loop error: {e}")
                time.sleep(1)

    def _capture_and_analyze(self) -> Optional[ScreenContext]:
        """Capture screen and perform analysis"""
        import pyautogui
        import cv2
        import numpy as np
        from datetime import datetime

        try:
            # Get current window info
            active_window = self._get_active_window()
            if not active_window:
                return None

            app_name, window_title = active_window

            # Check if we should monitor this application
            if not self.rules.should_monitor_application(app_name, window_title):
                return None

            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)

            # Calculate screen hash for change detection
            screen_hash = hashlib.md5(screenshot_np.tobytes()).hexdigest()

            # Skip if no significant change
            if screen_hash == self.last_screen_hash:
                return None

            # Check change percentage
            if self.last_screen_hash:
                change_percentage = self._calculate_change_percentage(screenshot_np)
                if change_percentage < self.rules.monitoring_rules['min_change_threshold']:
                    return None

            self.last_screen_hash = screen_hash

            # Analyze UI elements
            ui_elements = self._detect_ui_elements(screenshot_np)

            # Extract text with OCR
            visible_text = self._extract_text_smart(screenshot_np)

            # Create context
            context = ScreenContext(
                timestamp=datetime.now().isoformat(),
                application=app_name,
                window_title=window_title,
                ui_elements=ui_elements,
                visible_text=visible_text,
                screen_hash=screen_hash,
                change_areas=[]
            )

            return context

        except Exception as e:
            print(f"⚠️ Screen analysis error: {e}")
            return None

# Global monitor instance
screen_monitor = ContinuousScreenMonitor()

@function_tool()
async def start_continuous_screen_monitoring() -> str:
    """
    🔄 Start continuous screen monitoring with intelligent analysis

    Returns:
        str: Monitoring status

    Features:
        - Real-time screen analysis
        - UI element detection
        - Smart text extraction
        - Privacy protection
        - Performance optimization
    """
    global screen_monitor

    try:
        result = screen_monitor.start_monitoring()

        # Provide monitoring info
        rules_info = (
            f"📊 Monitoring Configuration:\n"
            f"🎯 Max FPS: {screen_monitor.rules.monitoring_rules['max_fps']}\n"
            f"🔒 Privacy Mode: {'ON' if screen_monitor.rules.monitoring_rules['privacy_mode'] else 'OFF'}\n"
            f"💾 Smart Caching: {'ON' if screen_monitor.rules.monitoring_rules['smart_caching'] else 'OFF'}\n"
            f"📈 Change Threshold: {screen_monitor.rules.monitoring_rules['min_change_threshold']}%"
        )

        return f"{result}\n\n{rules_info}"

    except Exception as e:
        return f"❌ Failed to start monitoring: {str(e)}"


@function_tool()
async def stop_continuous_screen_monitoring() -> str:
    """
    🛑 Stop continuous screen monitoring

    Returns:
        str: Stop confirmation
    """
    global screen_monitor

    try:
        result = screen_monitor.stop_monitoring()

        # Provide summary
        history_count = len(screen_monitor.screen_history)
        summary = (
            f"{result}\n"
            f"📊 Session Summary:\n"
            f"📸 Screens Analyzed: {history_count}\n"
            f"💾 Memory Usage: Optimized\n"
            f"🔒 Privacy: Protected"
        )

        return summary

    except Exception as e:
        return f"❌ Failed to stop monitoring: {str(e)}"

# Add missing methods to ContinuousScreenMonitor class
def _add_missing_methods_to_monitor():
    """Add missing methods to the ContinuousScreenMonitor class"""

    def _get_active_window(self) -> Optional[tuple]:
        """Get active window information"""
        try:
            import pygetwindow as gw
            active = gw.getActiveWindow()
            if active:
                return (active.title.split(' - ')[-1] if ' - ' in active.title else 'Unknown', active.title)
        except:
            pass
        return None

    def _calculate_change_percentage(self, current_frame) -> float:
        """Calculate percentage of screen that changed"""
        if not hasattr(self, '_last_frame') or self._last_frame is None:
            self._last_frame = current_frame
            return 100.0

        try:
            import cv2

            # Convert to grayscale for comparison
            gray1 = cv2.cvtColor(self._last_frame, cv2.COLOR_RGB2GRAY)
            gray2 = cv2.cvtColor(current_frame, cv2.COLOR_RGB2GRAY)

            # Calculate difference
            diff = cv2.absdiff(gray1, gray2)
            _, thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)

            # Calculate change percentage
            total_pixels = thresh.shape[0] * thresh.shape[1]
            changed_pixels = cv2.countNonZero(thresh)
            change_percentage = (changed_pixels / total_pixels) * 100

            self._last_frame = current_frame
            return change_percentage

        except Exception:
            return 100.0  # Assume change if calculation fails

    def _detect_ui_elements(self, screenshot_np) -> List[UIElement]:
        """Detect UI elements using computer vision"""
        elements = []

        try:
            import cv2

            # Convert to grayscale
            gray = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2GRAY)

            # Detect edges for UI elements
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                area = cv2.contourArea(contour)
                if 50 < area < 10000:  # Reasonable UI element size
                    x, y, w, h = cv2.boundingRect(contour)

                    # Classify element type based on dimensions
                    aspect_ratio = w / h if h > 0 else 1

                    if aspect_ratio > 3 and h < 50:
                        element_type = "textbox"
                    elif aspect_ratio < 2 and w < 200 and h < 100:
                        element_type = "button"
                    elif area > 1000:
                        element_type = "panel"
                    else:
                        element_type = "element"

                    element = UIElement(
                        type=element_type,
                        text="",  # Will be filled by OCR
                        position=(x, y, w, h),
                        confidence=0.8,
                        attributes={"area": area, "aspect_ratio": aspect_ratio}
                    )
                    elements.append(element)

        except Exception as e:
            print(f"⚠️ UI element detection error: {e}")

        return elements[:20]  # Limit to top 20 elements

    def _extract_text_smart(self, screenshot_np) -> List[str]:
        """Smart text extraction with caching and filtering"""
        try:
            import cv2

            # Convert to grayscale and enhance
            gray = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2GRAY)

            # Use cached result if screen hasn't changed much
            screen_hash = hashlib.md5(gray.tobytes()).hexdigest()
            if screen_hash in self.element_cache:
                return self.element_cache[screen_hash]

            # Try OCR if available
            if TESSERACT_AVAILABLE:
                try:
                    import pytesseract

                    # Perform OCR
                    text_data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)

                    extracted_text = []
                    for i in range(len(text_data['text'])):
                        confidence = int(text_data['conf'][i])
                        if confidence > self.rules.monitoring_rules['ocr_confidence_threshold']:
                            text = text_data['text'][i].strip()
                            if text and len(text) > 1:
                                # Apply privacy filtering
                                filtered_text = self.rules.filter_sensitive_text(text)
                                if filtered_text != '[SENSITIVE-CONTENT-HIDDEN]':
                                    extracted_text.append(filtered_text)

                    # Cache result
                    if len(self.element_cache) > 10:  # Limit cache size
                        self.element_cache.clear()
                    self.element_cache[screen_hash] = extracted_text

                    return extracted_text

                except Exception as e:
                    return [f"[OCR error: {str(e)}]"]
            else:
                return ["[OCR not available - run 'python install_tesseract.py' to install]"]

        except Exception as e:
            return [f"[Text extraction error: {str(e)}]"]

    def _update_history(self, context: ScreenContext):
        """Update screen history with memory management"""
        self.screen_history.append(context)

        # Limit history size
        max_items = self.rules.monitoring_rules['max_history_items']
        if len(self.screen_history) > max_items:
            self.screen_history = self.screen_history[-max_items:]

    # Add methods to the class
    ContinuousScreenMonitor._get_active_window = _get_active_window
    ContinuousScreenMonitor._calculate_change_percentage = _calculate_change_percentage
    ContinuousScreenMonitor._detect_ui_elements = _detect_ui_elements
    ContinuousScreenMonitor._extract_text_smart = _extract_text_smart
    ContinuousScreenMonitor._update_history = _update_history

# Call the function to add missing methods
_add_missing_methods_to_monitor()

# Methods successfully added to ContinuousScreenMonitor class


@function_tool()
async def get_current_screen_context() -> str:
    """
    📋 Get current screen context and analysis

    Returns:
        str: Current screen state analysis

    Features:
        - Current application info
        - Detected UI elements
        - Visible text summary
        - Screen activity status
    """
    global screen_monitor

    try:
        if not screen_monitor.is_running:
            return "⚠️ Screen monitoring is not active. Start monitoring first."

        context = screen_monitor.current_context
        if not context:
            return "📭 No screen context available yet. Please wait a moment..."

        # Build analysis report
        analysis = []
        analysis.append(f"🖥️ Current Screen Analysis")
        analysis.append(f"⏰ Timestamp: {context.timestamp}")
        analysis.append(f"📱 Application: {context.application}")
        analysis.append(f"🪟 Window: {context.window_title}")
        analysis.append(f"🔲 UI Elements: {len(context.ui_elements)} detected")

        # Show UI element types
        if context.ui_elements:
            element_types = {}
            for element in context.ui_elements:
                element_types[element.type] = element_types.get(element.type, 0) + 1

            type_summary = ", ".join([f"{count} {etype}s" for etype, count in element_types.items()])
            analysis.append(f"📊 Elements: {type_summary}")

        # Show text summary
        if context.visible_text:
            text_count = len(context.visible_text)
            analysis.append(f"📝 Text Elements: {text_count} detected")

            # Show sample text (first few items)
            if text_count > 0:
                sample_text = context.visible_text[:3]
                analysis.append(f"📄 Sample Text: {', '.join(sample_text)}")

        # Monitoring status
        analysis.append(f"🔄 Monitoring: {'Active' if screen_monitor.is_running else 'Inactive'}")
        analysis.append(f"📚 History: {len(screen_monitor.screen_history)} screens analyzed")

        return "\n".join(analysis)

    except Exception as e:
        return f"❌ Failed to get screen context: {str(e)}"


@function_tool()
async def analyze_screen_for_task(task_description: str) -> str:
    """
    🎯 Analyze current screen for specific task assistance

    Args:
        task_description: What the user wants to accomplish

    Returns:
        str: Task-specific screen analysis and suggestions

    Features:
        - Task-focused element detection
        - Contextual suggestions
        - Action recommendations
        - Smart assistance
    """
    global screen_monitor

    try:
        if not screen_monitor.is_running:
            return "⚠️ Screen monitoring is not active. Start monitoring first."

        context = screen_monitor.current_context
        if not context:
            return "📭 No screen context available. Please wait for screen analysis..."

        # Analyze based on task
        task_lower = task_description.lower()
        suggestions = []

        # Form filling assistance
        if any(word in task_lower for word in ['fill', 'form', 'input', 'enter']):
            textboxes = [e for e in context.ui_elements if e.type == 'textbox']
            if textboxes:
                suggestions.append(f"📝 Found {len(textboxes)} input fields on screen")
                suggestions.append("💡 I can help you fill forms or enter data")
            else:
                suggestions.append("🔍 No input fields detected on current screen")

        # Button/action assistance
        elif any(word in task_lower for word in ['click', 'button', 'submit', 'save']):
            buttons = [e for e in context.ui_elements if e.type == 'button']
            if buttons:
                suggestions.append(f"🔘 Found {len(buttons)} clickable buttons")
                suggestions.append("💡 I can help you click specific buttons or elements")
            else:
                suggestions.append("🔍 No buttons detected on current screen")

        # Reading/text assistance
        elif any(word in task_lower for word in ['read', 'text', 'content', 'information']):
            if context.visible_text:
                text_summary = f"Found {len(context.visible_text)} text elements"
                suggestions.append(f"📖 {text_summary}")
                suggestions.append("💡 I can read and summarize screen content for you")
            else:
                suggestions.append("📭 No readable text detected on current screen")

        # Navigation assistance
        elif any(word in task_lower for word in ['navigate', 'go to', 'find', 'search']):
            suggestions.append(f"🧭 Current location: {context.application}")
            suggestions.append("💡 I can help you navigate or find specific elements")

        # General assistance
        else:
            suggestions.append(f"🖥️ Analyzing screen for: {task_description}")
            suggestions.append(f"📱 Current app: {context.application}")
            suggestions.append(f"🔲 UI elements: {len(context.ui_elements)}")
            suggestions.append(f"📝 Text elements: {len(context.visible_text)}")

        # Add contextual suggestions based on current app
        app_lower = context.application.lower()
        if 'browser' in app_lower or 'chrome' in app_lower or 'firefox' in app_lower:
            suggestions.append("🌐 Web browser detected - I can help with web navigation")
        elif 'excel' in app_lower or 'word' in app_lower:
            suggestions.append("📊 Office application detected - I can assist with document tasks")
        elif 'code' in app_lower or 'studio' in app_lower:
            suggestions.append("💻 Development environment detected - I can help with coding tasks")

        result = f"🎯 Task Analysis: {task_description}\n\n" + "\n".join(suggestions)
        return result

    except Exception as e:
        return f"❌ Task analysis failed: {str(e)}"


@function_tool()
async def smart_screen_assistant(action: str, parameters: str = "") -> str:
    """
    🧠 Intelligent screen assistant that chooses the best approach

    Args:
        action: What to do ("capture", "monitor", "analyze", "click", "move_mouse")
        parameters: Additional parameters as string

    Returns:
        str: Action result with intelligent fallbacks

    Features:
        - Automatically chooses best available method
        - Handles OCR availability gracefully
        - Provides intelligent fallbacks
        - Optimizes for user's current setup
    """
    global screen_monitor

    try:
        action = action.lower().strip()

        if action == "capture":
            # Smart screen capture with fallback
            if TESSERACT_AVAILABLE:
                return await capture_live_screen(analyze_content=True, save_screenshot=False)
            else:
                return await capture_live_screen_basic()

        elif action == "monitor_start":
            # Start intelligent monitoring
            return await start_continuous_screen_monitoring()

        elif action == "monitor_stop":
            # Stop monitoring
            return await stop_continuous_screen_monitoring()

        elif action == "context":
            # Get current screen context
            return await get_current_screen_context()

        elif action == "analyze":
            # Analyze for specific task
            task = parameters if parameters else "general assistance"
            return await analyze_screen_for_task(task)

        elif action == "click":
            # Smart clicking with fallback
            if not parameters:
                return "❌ Please specify text to click on"

            if TESSERACT_AVAILABLE:
                return await click_on_text(parameters)
            else:
                return "❌ Text clicking requires OCR. Run 'python install_tesseract.py' to install Tesseract."

        elif action == "move_mouse":
            # Parse coordinates from parameters
            try:
                coords = parameters.split(",")
                if len(coords) >= 2:
                    x, y = int(coords[0].strip()), int(coords[1].strip())
                    duration = float(coords[2].strip()) if len(coords) > 2 else 0.5
                    click = coords[3].strip().lower() == "true" if len(coords) > 3 else False
                    return await move_mouse_to_position(x, y, duration, click)
                else:
                    return "❌ Please provide coordinates as 'x,y' or 'x,y,duration,click'"
            except ValueError:
                return "❌ Invalid coordinates format. Use: 'x,y' or 'x,y,duration,click'"

        elif action == "mouse_position":
            # Get current mouse position
            return await get_mouse_position()

        elif action == "activity":
            # Monitor screen activity
            try:
                duration = int(parameters) if parameters else 5
                return await analyze_screen_activity(duration, 30)
            except ValueError:
                return await analyze_screen_activity(5, 30)

        elif action == "changes":
            # Monitor screen changes
            area = parameters if parameters else "full"
            return await monitor_screen_changes(area, 50)

        else:
            return f"❌ Unknown action: {action}. Available: capture, monitor_start, monitor_stop, context, analyze, click, move_mouse, mouse_position, activity, changes"

    except Exception as e:
        return f"❌ Smart screen assistant error: {str(e)}"


@function_tool()
async def screen_status_report() -> str:
    """
    📊 Get comprehensive status of screen monitoring capabilities

    Returns:
        str: Detailed status report

    Features:
        - OCR availability status
        - Monitoring system status
        - Available functions
        - Configuration summary
    """
    global screen_monitor

    try:
        status = []
        status.append("📊 ZARA Screen Monitoring Status Report")
        status.append("=" * 50)

        # OCR Status
        if TESSERACT_AVAILABLE:
            try:
                import pytesseract
                version = pytesseract.get_tesseract_version()
                status.append(f"✅ Tesseract OCR: Available (v{version})")
            except:
                status.append("⚠️ Tesseract OCR: Configured but not working properly")
        else:
            status.append("❌ Tesseract OCR: Not available")
            status.append("   💡 Run 'python install_tesseract.py' to install")

        # Monitoring Status
        if screen_monitor.is_running:
            status.append(f"🔄 Continuous Monitoring: Active")
            status.append(f"📚 Screens Analyzed: {len(screen_monitor.screen_history)}")
            if screen_monitor.current_context:
                ctx = screen_monitor.current_context
                status.append(f"📱 Current App: {ctx.application}")
                status.append(f"🔲 UI Elements: {len(ctx.ui_elements)}")
        else:
            status.append("⏸️ Continuous Monitoring: Inactive")

        # Available Functions
        status.append("\n🛠️ Available Functions:")
        basic_functions = [
            "✅ capture_live_screen_basic() - Always available",
            "✅ move_mouse_to_position() - Always available",
            "✅ get_mouse_position() - Always available",
            "✅ analyze_screen_activity() - Always available",
            "✅ monitor_screen_changes() - Always available",
            "✅ start/stop_continuous_screen_monitoring() - Always available"
        ]

        ocr_functions = [
            f"{'✅' if TESSERACT_AVAILABLE else '❌'} capture_live_screen() - Requires OCR",
            f"{'✅' if TESSERACT_AVAILABLE else '❌'} click_on_text() - Requires OCR"
        ]

        for func in basic_functions:
            status.append(f"  {func}")
        for func in ocr_functions:
            status.append(f"  {func}")

        # Configuration
        status.append(f"\n⚙️ Configuration:")
        status.append(f"  🎯 Max FPS: {screen_monitor.rules.monitoring_rules['max_fps']}")
        status.append(f"  🔒 Privacy Mode: {'ON' if screen_monitor.rules.monitoring_rules['privacy_mode'] else 'OFF'}")
        status.append(f"  💾 Smart Caching: {'ON' if screen_monitor.rules.monitoring_rules['smart_caching'] else 'OFF'}")

        return "\n".join(status)

    except Exception as e:
        return f"❌ Status report failed: {str(e)}"


# ==========================================
# 🎭 HUMANIOUS AI INTEGRATION TOOLS
# ==========================================

@function_tool()
async def analyze_user_emotion_and_respond(user_message: str) -> str:
    """
    🧠 Analyze user emotion and generate empathetic response

    Args:
        user_message: User's message to analyze

    Returns:
        str: Emotional analysis and empathetic response

    Features:
        - Advanced emotion detection
        - Cultural sensitivity
        - Empathetic response generation
        - Relationship building
    """
    try:
        # Import humanious systems
        from personality_engine import analyze_user_emotion, get_personality_response
        from human_behavior import human_behavior
        from relationship_memory import advanced_memory

        # Analyze user emotion
        emotion_analysis = analyze_user_emotion(user_message)

        # Generate personality response
        personality_response = get_personality_response(
            'general', emotion_analysis['detected_emotion']
        )

        # Generate empathetic response if needed
        if emotion_analysis.get('should_be_supportive', False):
            empathic_response = human_behavior.generate_empathetic_response(
                emotion_analysis['detected_emotion'],
                user_message,
                emotion_analysis['intensity']
            )

            response = f"{emotion_analysis['empathetic_response']}\n\n{empathic_response.response}"
        else:
            response = f"{personality_response['phrase']} {personality_response['expression']}"

        # Record interaction for learning
        advanced_memory.record_interaction(
            'emotional_support', user_message,
            emotion_analysis['detected_emotion'], 'empathetic_response', 0.9
        )

        # Build relationship memory if significant emotion
        if emotion_analysis['intensity'] > 0.6:
            advanced_memory.store_relationship_memory(
                'emotional_moment',
                f"User felt {emotion_analysis['detected_emotion']} about: {user_message[:100]}",
                emotion_analysis['intensity'],
                emotion_analysis['detected_emotion']
            )

        return f"🧠 **Emotional Analysis**: {emotion_analysis['detected_emotion'].title()} (Intensity: {emotion_analysis['intensity']:.1%})\n\n💝 **My Response**: {response}"

    except Exception as e:
        return f"❌ Emotional analysis failed: {str(e)}"


@function_tool()
async def generate_humanious_response(user_message: str, context: str = "general") -> str:
    """
    🎭 Generate complete humanious response with personality, empathy, and intelligence

    Args:
        user_message: User's message
        context: Context of the conversation

    Returns:
        str: Complete humanious response

    Features:
        - Full personality integration
        - Emotional intelligence
        - Predictive assistance
        - Relationship building
        - Cultural sensitivity
    """
    try:
        # Import all humanious systems
        from personality_engine import analyze_user_emotion, get_personality_response
        from human_behavior import human_behavior
        from conversation_manager import conversation_manager
        from intelligence_engine import intelligence_engine
        from predictive_assistant import predictive_assistant
        from relationship_memory import advanced_memory

        # Analyze user patterns and emotion
        emotion_analysis = analyze_user_emotion(user_message)
        user_patterns = advanced_memory.analyze_user_patterns()

        # Generate contextual response
        if conversation_manager.current_context:
            response_data = conversation_manager.continue_conversation(user_message, context)
        else:
            response_data = conversation_manager.start_conversation(user_message)

        base_response = response_data['message']

        # Add human-like enhancements
        enhanced_response = human_behavior.make_response_more_human(
            base_response,
            {
                'topic': context,
                'user_mood': emotion_analysis['detected_emotion'],
                'relationship_strength': user_patterns.get('relationship_strength', 0.5)
            }
        )

        # Add predictive assistance if appropriate
        if predictive_assistant.should_offer_proactive_help(
            {'user_emotion': emotion_analysis['detected_emotion']}, user_patterns
        ):
            insights = predictive_assistant.generate_predictive_insights(context, user_patterns)
            suggestions = predictive_assistant.generate_proactive_suggestions(context, user_patterns)

            predictive_content = predictive_assistant.format_predictive_response(insights, suggestions)
            if predictive_content:
                enhanced_response += f"\n\n{predictive_content}"

        # Add empathetic elements if needed
        if emotion_analysis.get('should_be_supportive', False):
            empathic_response = human_behavior.generate_empathetic_response(
                emotion_analysis['detected_emotion'],
                user_message,
                emotion_analysis['intensity']
            )
            enhanced_response = f"{empathic_response.response}\n\n{enhanced_response}"

        return enhanced_response

    except Exception as e:
        return f"❌ Humanious response generation failed: {str(e)}"


@function_tool()
async def solve_problem_with_human_reasoning(problem: str, context: str = "") -> str:
    """
    🧠 Solve problems using human-like reasoning and intelligence

    Args:
        problem: Problem to solve
        context: Additional context

    Returns:
        str: Solution with human-like reasoning explanation

    Features:
        - Multi-step reasoning
        - Creative problem solving
        - Human-like explanation
        - Alternative approaches
    """
    try:
        from intelligence_engine import intelligence_engine
        from human_behavior import human_behavior

        # Solve problem with advanced reasoning
        solution = intelligence_engine.solve_problem(problem, context)

        # Generate human-like explanation
        explanation = intelligence_engine.explain_reasoning(solution)

        # Add human-like touches
        enhanced_explanation = human_behavior.make_response_more_human(
            explanation,
            {'topic': 'problem_solving', 'user_mood': 'focused'}
        )

        return enhanced_explanation

    except Exception as e:
        return f"❌ Problem solving failed: {str(e)}"


@function_tool()
async def get_humanious_status_report() -> str:
    """
    📊 Get comprehensive status of all humanious AI systems

    Returns:
        str: Complete status report of personality, memory, and intelligence systems
    """
    try:
        from relationship_memory import advanced_memory
        from personality_engine import zara_personality

        # Get user patterns and relationship strength
        user_patterns = advanced_memory.analyze_user_patterns()
        relationship_strength = user_patterns.get('relationship_strength', 0.0)

        status = []
        status.append("🎭 ZARA Humanious AI Status Report")
        status.append("=" * 50)

        # Personality System Status
        status.append("🎭 **Personality System**: ✅ Active")
        status.append(f"   • Friendliness: {zara_personality.traits['friendliness'].value:.1%}")
        status.append(f"   • Empathy: {zara_personality.traits['empathy'].value:.1%}")
        status.append(f"   • Helpfulness: {zara_personality.traits['helpfulness'].value:.1%}")
        status.append(f"   • Cultural Awareness: {zara_personality.traits['cultural_awareness'].value:.1%}")

        # Relationship System Status
        status.append(f"\n💝 **Relationship System**: ✅ Active")
        status.append(f"   • Relationship Strength: {relationship_strength:.1%}")
        status.append(f"   • Learned Preferences: {len(user_patterns.get('learned_preferences', {}))}")
        status.append(f"   • Emotional Patterns: {len(user_patterns.get('emotion_patterns', {}))}")

        # Intelligence System Status
        status.append(f"\n🧠 **Intelligence System**: ✅ Active")
        status.append(f"   • Reasoning Capabilities: Multi-type (Logical, Creative, Analytical)")
        status.append(f"   • Problem Solving: Advanced with explanation")
        status.append(f"   • Predictive Assistance: Context-aware")

        # Human Behavior System Status
        status.append(f"\n🎭 **Human Behavior System**: ✅ Active")
        status.append(f"   • Emotional Intelligence: Advanced")
        status.append(f"   • Cultural Expressions: Hindi/English mix")
        status.append(f"   • Humor & Personality: Contextual")

        # Conversation System Status
        status.append(f"\n💬 **Conversation System**: ✅ Active")
        status.append(f"   • Natural Flow: Context-aware")
        status.append(f"   • Memory Integration: Relationship-based")
        status.append(f"   • Proactive Assistance: Pattern-based")

        status.append(f"\n🌟 **Overall Humanious Level**: {(relationship_strength + 0.8) / 2:.1%}")
        status.append(f"🎯 **Ready for**: Natural conversation, emotional support, intelligent assistance")

        return "\n".join(status)

    except Exception as e:
        return f"❌ Humanious status report failed: {str(e)}"


