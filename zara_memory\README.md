# ZARA Memory System

This directory contains ZARA's memory and data storage components.

## Files:

### Database Files:
- `chat_history.db` - SQLite database for conversation history
- `reminders.db` - User reminders and scheduled tasks
- `user_preferences.db` - Personalization settings

### Cache Files:
- `analysis_cache/` - Cached data analysis results
- `temp/` - Temporary files and processing data

### Logs:
- `conversation_logs/` - Detailed conversation transcripts
- `error_logs/` - Error and debugging information

## Database Schema:

### chat_messages table:
- id (INTEGER PRIMARY KEY)
- timestamp (DATETIME)
- role (TEXT) - 'user' or 'assistant'
- content (TEXT) - Message content
- metadata (JSON) - Additional context

### reminders table:
- id (INTEGER PRIMARY KEY)
- created_at (DATETIME)
- reminder_date (DATE)
- reminder_time (TIME)
- message (TEXT)
- status (TEXT) - 'active', 'completed', 'cancelled'

This memory system enables ZARA to:
- Remember past conversations
- Learn user preferences
- Provide contextual responses
- Manage reminders and tasks
- Maintain conversation continuity

Created by: <PERSON><PERSON>
