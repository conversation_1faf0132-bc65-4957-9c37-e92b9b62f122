#!/usr/bin/env python3
"""
Zara Status Writer Mo<PERSON>le
Created by: <PERSON><PERSON>

This module provides functions for <PERSON><PERSON> to write real-time status updates
that the GUI can monitor for perfect synchronization.
"""

import json
import os
import time
import threading
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

class ZaraStatusWriter:
    """Writes real-time status updates for GUI synchronization"""
    
    def __init__(self, status_file: str = "zara_status.json"):
        self.status_file = Path(status_file)
        self.lock = threading.Lock()
        
        # Current status
        self.current_status = {
            'status': 'idle',
            'voice_active': False,
            'connected': True,
            'last_response': '',
            'timestamp': datetime.now().isoformat(),
            'session_id': f"session_{int(time.time())}",
            'uptime': 0,
            'start_time': time.time()
        }
        
        # Initialize status file
        self.write_status()
    
    def update_status(self, status: str, **kwargs):
        """Update the current status"""
        with self.lock:
            self.current_status['status'] = status
            self.current_status['timestamp'] = datetime.now().isoformat()
            self.current_status['uptime'] = time.time() - self.current_status['start_time']
            
            # Update any additional fields
            for key, value in kwargs.items():
                self.current_status[key] = value
            
            self.write_status()
    
    def set_voice_active(self, active: bool):
        """Set voice activity status"""
        with self.lock:
            self.current_status['voice_active'] = active
            self.current_status['timestamp'] = datetime.now().isoformat()
            self.write_status()
    
    def set_response(self, response: str):
        """Set the last response"""
        with self.lock:
            self.current_status['last_response'] = response
            self.current_status['timestamp'] = datetime.now().isoformat()
            self.write_status()
    
    def set_connected(self, connected: bool):
        """Set connection status"""
        with self.lock:
            self.current_status['connected'] = connected
            self.current_status['timestamp'] = datetime.now().isoformat()
            self.write_status()
    
    def write_status(self):
        """Write status to file"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_status, f, indent=2)
        except Exception as e:
            print(f"Error writing status file: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status"""
        with self.lock:
            return self.current_status.copy()

# Global status writer instance
status_writer = ZaraStatusWriter()

# Convenience functions for easy integration
def update_zara_status(status: str, **kwargs):
    """Update Zara status"""
    status_writer.update_status(status, **kwargs)

def set_zara_voice_active(active: bool):
    """Set voice activity"""
    status_writer.set_voice_active(active)

def set_zara_response(response: str):
    """Set last response"""
    status_writer.set_response(response)

def set_zara_connected(connected: bool):
    """Set connection status"""
    status_writer.set_connected(connected)

def get_zara_status() -> Dict[str, Any]:
    """Get current status"""
    return status_writer.get_status()